# Overall API Server Configuration

server:
  host: "0.0.0.0"  # 监听所有网络接口，支持局域网访问
  port: 5631
  workers: 1
  log_level: "info"

ollama:
  base_url: "http://localhost:11434"
  timeout: 300  # 5分钟超时
  max_connections: 10

memory:
  threshold: 0.90  # 90% 内存阈值
  check_interval: 5  # 每5秒检查一次内存使用情况
  
session:
  idle_timeout: 1800  # 30分钟空闲后释放会话
  max_sessions: 8  # 最大并发会话数
  cleanup_interval: 300  # 每5分钟检查空闲会话

queue:
  max_size: 100  # 最大队列长度
  task_timeout: 3600  # 任务超时时间（1小时）

database:
  url: "sqlite+aiosqlite:///./data/database/app.db"
  echo: false  # 生产环境设为false

test_plan:
  prompts_file: "app/data/prompts/test_plan_prompts.json"
  default_model: "deepseek-r1:32b"
  platform_state_file: "data/.platform_state.json"

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  max_file_size: 10  # MB
  backup_count: 5
  files:
    app: "logs/app.log"
    api: "logs/api.log" 
    error: "logs/error.log"