#!/usr/bin/env python3
"""
Ollama Local API 测试套件
测试所有API接口的功能完整性
"""

import asyncio
import httpx
import json
import time
import uuid
import sys
import os
from typing import Dict, Any, List, Optional
from pathlib import Path

# 添加项目路径到系统路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class OllamaLocalAPITester:
    """Ollama本地API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:5631"):
        self.base_url = base_url
        # 忽略系统代理设置，直接连接本地服务
        self.client = httpx.AsyncClient(
            timeout=120.0,  # 增加超时时间到2分钟，因为LLM生成需要较长时间
            proxies={},  # 明确设置为空代理
            trust_env=False  # 不信任环境变量中的代理设置
        )
        self.test_results = []
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def log_test(self, test_name: str, success: bool, message: str = "", data: Any = None):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")
        if data and isinstance(data, dict):
            print(f"    Data: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
        
        self.test_results.append({
            "test_name": test_name,
            "success": success,
            "message": message,
            "data": data
        })
    
    async def test_health_check(self):
        """测试健康检查接口"""
        print("\n🔍 测试健康检查接口")
        
        try:
            response = await self.client.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                data = response.json()
                expected_keys = ["status", "services", "system"]
                
                if all(key in data for key in expected_keys):
                    self.log_test("健康检查接口", True, f"状态: {data.get('status')}", data)
                else:
                    self.log_test("健康检查接口", False, f"响应缺少必要字段: {expected_keys}")
            else:
                self.log_test("健康检查接口", False, f"HTTP状态码: {response.status_code}")
                
        except Exception as e:
            self.log_test("健康检查接口", False, f"请求异常: {str(e)}")
    
    async def test_root_endpoint(self):
        """测试根路径接口"""
        print("\n🏠 测试根路径接口")
        
        try:
            response = await self.client.get(f"{self.base_url}/")
            
            if response.status_code == 200:
                data = response.json()
                expected_keys = ["service", "version", "status", "endpoints"]
                
                if all(key in data for key in expected_keys):
                    endpoints = data.get("endpoints", {})
                    if "test_plan" in endpoints:
                        self.log_test("根路径接口", True, f"服务: {data.get('service')}", data)
                    else:
                        self.log_test("根路径接口", False, "缺少test_plan端点信息")
                else:
                    self.log_test("根路径接口", False, f"响应缺少必要字段: {expected_keys}")
            else:
                self.log_test("根路径接口", False, f"HTTP状态码: {response.status_code}")
                
        except Exception as e:
            self.log_test("根路径接口", False, f"请求异常: {str(e)}")
    
    async def test_ollama_models(self):
        """测试获取模型列表"""
        print("\n📋 测试Ollama模型列表接口")
        
        try:
            response = await self.client.get(f"{self.base_url}/ollama/models")
            
            if response.status_code == 200:
                data = response.json()
                if "models" in data and isinstance(data["models"], list):
                    model_count = len(data["models"])
                    self.log_test("Ollama模型列表", True, f"找到 {model_count} 个模型")
                    return data["models"]
                else:
                    self.log_test("Ollama模型列表", False, "响应格式不正确")
                    return []
            else:
                self.log_test("Ollama模型列表", False, f"HTTP状态码: {response.status_code}")
                return []
                
        except Exception as e:
            self.log_test("Ollama模型列表", False, f"请求异常: {str(e)}")
            return []
    
    async def test_ollama_generate(self, models: List[Dict]):
        """测试Ollama文本生成"""
        print("\n💬 测试Ollama文本生成接口")
        
        if not models:
            self.log_test("Ollama文本生成", False, "没有可用模型")
            return
        
        # 选择第一个模型进行测试
        test_model = models[0]["name"]
        
        # 测试非流式生成
        try:
            request_data = {
                "model": test_model,
                "prompt": "说一句简短的问候语",
                "stream": False,
                "options": {
                    "temperature": 0.1
                }
            }
            
            start_time = time.time()
            response = await self.client.post(
                f"{self.base_url}/ollama/generate",
                json=request_data
            )
            elapsed_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if "response" in data and data["response"]:
                    self.log_test(
                        "Ollama非流式生成", 
                        True, 
                        f"模型: {test_model}, 耗时: {elapsed_time:.2f}s"
                    )
                else:
                    self.log_test("Ollama非流式生成", False, "响应中没有生成的文本")
            else:
                self.log_test("Ollama非流式生成", False, f"HTTP状态码: {response.status_code}")
                
        except Exception as e:
            self.log_test("Ollama非流式生成", False, f"请求异常: {str(e)}")
    
    async def test_ollama_chat(self, models: List[Dict]):
        """测试Ollama聊天接口"""
        print("\n🗨️ 测试Ollama聊天接口")
        
        if not models:
            self.log_test("Ollama聊天", False, "没有可用模型")
            return
        
        # 选择第一个模型进行测试
        test_model = models[0]["name"]
        
        try:
            request_data = {
                "model": test_model,
                "messages": [
                    {"role": "user", "content": "你好，请简短回复"}
                ],
                "stream": False,
                "options": {
                    "temperature": 0.1
                }
            }
            
            start_time = time.time()
            response = await self.client.post(
                f"{self.base_url}/ollama/chat",
                json=request_data
            )
            elapsed_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if "message" in data and "content" in data["message"]:
                    self.log_test(
                        "Ollama聊天", 
                        True, 
                        f"模型: {test_model}, 耗时: {elapsed_time:.2f}s"
                    )
                else:
                    self.log_test("Ollama聊天", False, "响应格式不正确")
            else:
                self.log_test("Ollama聊天", False, f"HTTP状态码: {response.status_code}")
                
        except Exception as e:
            self.log_test("Ollama聊天", False, f"请求异常: {str(e)}")
    
    async def test_test_plan_generate(self):
        """测试测试计划生成接口"""
        print("\n🧪 测试测试计划生成接口")
        
        test_cases = [
            {
                "name": "iOS简单测试（非流式）",
                "instruction": "点击按钮",
                "platform": "ios",
                "stream": False
            },
            {
                "name": "Android流式测试",
                "instruction": "打开淘宝应用，搜索手机，查看第一个商品详情",
                "platform": "android", 
                "stream": True
            },
            {
                "name": "复杂多步骤测试（流式）",
                "instruction": "启动微信，点击发现，进入朋友圈，刷新内容，点击第一条朋友圈，点赞后返回",
                "platform": "android",
                "stream": True
            }
        ]
        
        generated_records = []
        
        for test_case in test_cases:
            try:
                request_data = {
                    "instruction": test_case["instruction"],
                    "model": "deepseek-r1:32b",  # 使用配置中的默认模型
                    "stream": test_case.get("stream", False)
                }
                
                if "platform" in test_case:
                    request_data["platform"] = test_case["platform"]
                
                start_time = time.time()
                
                if test_case.get("stream", False):
                    # 流式请求测试
                    success = await self._test_stream_generation(test_case, request_data, start_time)
                    if success:
                        generated_records.append("stream_test_record")  # 占位符
                else:
                    # 非流式请求测试
                    response = await self.client.post(
                        f"{self.base_url}/test-plan/generate",
                        json=request_data
                    )
                    elapsed_time = time.time() - start_time
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        if data.get("status") == "success" and "plan" in data:
                            plan = data["plan"]
                            steps_count = plan.get("total_steps", 0)
                            platform = data.get("detected_platform", "unknown")
                            
                            generated_records.append(data.get("record_id"))
                            
                            self.log_test(
                                f"测试计划生成 - {test_case['name']}",
                                True,
                                f"平台: {platform}, 步骤数: {steps_count}, 耗时: {elapsed_time:.2f}s"
                            )
                        else:
                            self.log_test(
                                f"测试计划生成 - {test_case['name']}",
                                False,
                                f"生成失败: {data.get('error', '未知错误')}"
                            )
                    else:
                        self.log_test(
                            f"测试计划生成 - {test_case['name']}",
                            False,
                            f"HTTP状态码: {response.status_code}"
                        )
                    
                # 避免请求过快
                await asyncio.sleep(1)
                
            except Exception as e:
                self.log_test(
                    f"测试计划生成 - {test_case['name']}",
                    False,
                    f"请求异常: {str(e)}"
                )
        
        return generated_records
    
    async def _test_stream_generation(self, test_case: Dict, request_data: Dict, start_time: float) -> bool:
        """测试流式生成"""
        try:
            print(f"  开始流式生成: {test_case['name']}")
            
            async with self.client.stream(
                "POST",
                f"{self.base_url}/test-plan/generate", 
                json=request_data
            ) as response:
                
                if response.status_code != 200:
                    self.log_test(
                        f"测试计划生成 - {test_case['name']}",
                        False,
                        f"HTTP状态码: {response.status_code}"
                    )
                    return False
                
                progress_count = 0
                final_result = None
                accumulated_buffer = ""  # 用于累积不完整的数据
                
                async for chunk in response.aiter_text():
                    if chunk.strip():
                        # 将新数据添加到缓冲区
                        accumulated_buffer += chunk
                        
                        # 按行分割处理完整的SSE消息
                        while '\n' in accumulated_buffer:
                            line, accumulated_buffer = accumulated_buffer.split('\n', 1)
                            line = line.strip()
                            
                            if line.startswith('data: '):
                                try:
                                    json_str = line[6:].strip()  # 去掉 "data: " 前缀
                                    
                                    if not json_str:  # 跳过空数据行
                                        continue
                                    
                                    data = json.loads(json_str)
                                    
                                    if data.get("type") == "progress":
                                        progress_count += 1
                                        if progress_count % 10 == 0:  # 每10个chunk打印一次进度
                                            print(f"    进度: {data.get('accumulated_length', 0)} 字符")
                                    
                                    elif data.get("type") == "final":
                                        final_result = data
                                        elapsed_time = time.time() - start_time
                                        
                                        if data.get("status") == "success":
                                            plan = data.get("plan", {})
                                            steps_count = plan.get("total_steps", 0)
                                            platform = data.get("detected_platform", "unknown")
                                            
                                            self.log_test(
                                                f"测试计划生成 - {test_case['name']}（流式）",
                                                True,
                                                f"平台: {platform}, 步骤数: {steps_count}, 耗时: {elapsed_time:.2f}s, 进度块: {progress_count}"
                                            )
                                            return True
                                        else:
                                            self.log_test(
                                                f"测试计划生成 - {test_case['name']}（流式）",
                                                False,
                                                f"生成失败: {data.get('error', '未知错误')}"
                                            )
                                            return False
                                            
                                except json.JSONDecodeError as e:
                                    # 打印详细错误信息以便调试
                                    print(f"    JSON解析错误: {e}")
                                    print(f"    原始数据: {repr(json_str)}")
                                    continue
                                except Exception as e:
                                    print(f"    处理数据时发生错误: {e}")
                                    continue
                
                # 如果没有收到最终结果
                if not final_result:
                    self.log_test(
                        f"测试计划生成 - {test_case['name']}（流式）",
                        False,
                        "流式响应意外结束"
                    )
                    return False
                    
        except Exception as e:
            elapsed_time = time.time() - start_time
            self.log_test(
                f"测试计划生成 - {test_case['name']}（流式）",
                False,
                f"流式请求异常: {str(e)}, 耗时: {elapsed_time:.2f}s"
            )
            return False
    
    async def test_test_plan_records(self, record_ids: List[str]):
        """测试测试计划记录接口"""
        print("\n📄 测试测试计划记录接口")
        
        # 测试记录列表接口
        try:
            response = await self.client.get(f"{self.base_url}/test-plan/records")
            
            if response.status_code == 200:
                data = response.json()
                expected_keys = ["records", "total", "page", "page_size", "has_next"]
                
                if all(key in data for key in expected_keys):
                    total_records = data.get("total", 0)
                    records_count = len(data.get("records", []))
                    self.log_test(
                        "测试计划记录列表",
                        True,
                        f"总记录数: {total_records}, 当前页: {records_count}"
                    )
                else:
                    self.log_test("测试计划记录列表", False, "响应格式不正确")
            else:
                self.log_test("测试计划记录列表", False, f"HTTP状态码: {response.status_code}")
                
        except Exception as e:
            self.log_test("测试计划记录列表", False, f"请求异常: {str(e)}")
        
        # 测试带参数的记录查询
        try:
            params = {
                "page": 1,
                "page_size": 5,
                "search": "美团",
                "platform": "ios",
                "status": "success"
            }
            
            response = await self.client.get(f"{self.base_url}/test-plan/records", params=params)
            
            if response.status_code == 200:
                data = response.json()
                filtered_count = len(data.get("records", []))
                self.log_test("测试计划记录搜索", True, f"筛选后记录数: {filtered_count}")
            else:
                self.log_test("测试计划记录搜索", False, f"HTTP状态码: {response.status_code}")
                
        except Exception as e:
            self.log_test("测试计划记录搜索", False, f"请求异常: {str(e)}")
        
        # 测试单个记录详情接口
        if record_ids:
            try:
                record_id = record_ids[0]  # 使用第一个记录ID
                response = await self.client.get(f"{self.base_url}/test-plan/records/{record_id}")
                
                if response.status_code == 200:
                    data = response.json()
                    if "record_id" in data and "generated_plan" in data:
                        self.log_test("测试计划记录详情", True, f"记录ID: {record_id}")
                    else:
                        self.log_test("测试计划记录详情", False, "响应格式不正确")
                elif response.status_code == 404:
                    self.log_test("测试计划记录详情", False, "记录不存在")
                else:
                    self.log_test("测试计划记录详情", False, f"HTTP状态码: {response.status_code}")
                    
            except Exception as e:
                self.log_test("测试计划记录详情", False, f"请求异常: {str(e)}")
    
    async def test_test_plan_stats(self):
        """测试测试计划统计接口"""
        print("\n📊 测试测试计划统计接口")
        
        try:
            response = await self.client.get(f"{self.base_url}/test-plan/stats")
            
            if response.status_code == 200:
                data = response.json()
                expected_keys = [
                    "total_records", "success_records", "failed_records", 
                    "error_records", "success_rate", "platform_distribution", "recent_activity"
                ]
                
                if all(key in data for key in expected_keys):
                    success_rate = data.get("success_rate", 0)
                    total_records = data.get("total_records", 0)
                    self.log_test(
                        "测试计划统计",
                        True,
                        f"总记录: {total_records}, 成功率: {success_rate}%"
                    )
                else:
                    self.log_test("测试计划统计", False, "响应格式不正确")
            else:
                self.log_test("测试计划统计", False, f"HTTP状态码: {response.status_code}")
                
        except Exception as e:
            self.log_test("测试计划统计", False, f"请求异常: {str(e)}")
    
    async def test_test_plan_platform_info(self):
        """测试测试计划平台信息接口"""
        print("\n📱 测试测试计划平台信息接口")
        
        try:
            response = await self.client.get(f"{self.base_url}/test-plan/platform-info")
            
            if response.status_code == 200:
                data = response.json()
                if "current_platform" in data and "next_platform" in data:
                    current = data.get("current_platform")
                    next_platform = data.get("next_platform")
                    self.log_test(
                        "测试计划平台信息",
                        True,
                        f"当前: {current}, 下次: {next_platform}"
                    )
                else:
                    self.log_test("测试计划平台信息", False, "响应格式不正确")
            else:
                self.log_test("测试计划平台信息", False, f"HTTP状态码: {response.status_code}")
                
        except Exception as e:
            self.log_test("测试计划平台信息", False, f"请求异常: {str(e)}")
    
    async def test_error_cases(self):
        """测试错误情况处理"""
        print("\n⚠️ 测试错误情况处理")
        
        # 测试不存在的接口
        try:
            response = await self.client.get(f"{self.base_url}/non-existent-endpoint")
            
            if response.status_code == 404:
                self.log_test("404错误处理", True, "正确返回404状态码")
            else:
                self.log_test("404错误处理", False, f"意外状态码: {response.status_code}")
                
        except Exception as e:
            self.log_test("404错误处理", False, f"请求异常: {str(e)}")
        
        # 测试无效的测试计划生成请求
        try:
            response = await self.client.post(
                f"{self.base_url}/test-plan/generate",
                json={"instruction": ""}  # 空指令
            )
            
            if response.status_code == 422:  # Validation error
                self.log_test("请求验证错误", True, "正确返回验证错误")
            else:
                self.log_test("请求验证错误", False, f"意外状态码: {response.status_code}")
                
        except Exception as e:
            self.log_test("请求验证错误", False, f"请求异常: {str(e)}")
        
        # 测试不存在的记录ID
        try:
            fake_id = str(uuid.uuid4())
            response = await self.client.get(f"{self.base_url}/test-plan/records/{fake_id}")
            
            if response.status_code == 404:
                self.log_test("记录不存在错误", True, "正确返回404状态码")
            else:
                self.log_test("记录不存在错误", False, f"意外状态码: {response.status_code}")
                
        except Exception as e:
            self.log_test("记录不存在错误", False, f"请求异常: {str(e)}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始Ollama本地API完整测试")
        print(f"📍 测试地址: {self.base_url}")
        print("=" * 80)
        
        start_time = time.time()
        
        # 基础接口测试
        await self.test_health_check()
        await self.test_root_endpoint()
        
        # Ollama接口测试
        models = await self.test_ollama_models()
        if models:
            await self.test_ollama_generate(models)
            await self.test_ollama_chat(models)
        
        # 测试计划接口测试
        record_ids = await self.test_test_plan_generate()
        await self.test_test_plan_records(record_ids)
        await self.test_test_plan_stats()
        await self.test_test_plan_platform_info()
        
        # 错误情况测试
        await self.test_error_cases()
        
        # 输出测试结果
        total_time = time.time() - start_time
        self.print_test_summary(total_time)
    
    def print_test_summary(self, total_time: float):
        """输出测试总结"""
        print("\n" + "=" * 80)
        print("📊 测试结果总结")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"总测试数量: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"总耗时: {total_time:.2f}秒")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        print("\n" + "=" * 80)
        if failed_tests == 0:
            print("🎉 所有测试通过！API服务运行正常。")
        else:
            print("⚠️ 有测试失败，请检查API服务状态。")
        print("=" * 80)

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Ollama本地API测试套件")
    parser.add_argument(
        "--base-url",
        default="http://localhost:5631",
        help="API服务基础URL (默认: http://localhost:5631)"
    )
    parser.add_argument(
        "--test",
        choices=["all", "basic", "ollama", "test-plan", "error"],
        default="all",
        help="运行指定的测试集合"
    )
    
    args = parser.parse_args()
    
    async with OllamaLocalAPITester(args.base_url) as tester:
        if args.test == "all":
            await tester.run_all_tests()
        elif args.test == "basic":
            await tester.test_health_check()
            await tester.test_root_endpoint()
            tester.print_test_summary(0)
        elif args.test == "ollama":
            models = await tester.test_ollama_models()
            if models:
                await tester.test_ollama_generate(models)
                await tester.test_ollama_chat(models)
            tester.print_test_summary(0)
        elif args.test == "test-plan":
            record_ids = await tester.test_test_plan_generate()
            await tester.test_test_plan_records(record_ids)
            await tester.test_test_plan_stats()
            await tester.test_test_plan_platform_info()
            tester.print_test_summary(0)
        elif args.test == "error":
            await tester.test_error_cases()
            tester.print_test_summary(0)

if __name__ == "__main__":
    asyncio.run(main())