# 🚀 Ollama 本地全能API服务器

一个智能的本地AI模型聚合服务，为你的ollama提供队列管理、内存监控和局域网访问能力。

## 🌟 核心特性

### 🎯 智能队列管理
- **内存监控**：实时监控系统内存使用率，超过90%自动启用队列模式
- **并发控制**：最多支持8个ollama会话同时运行
- **排队机制**：内存压力大时新请求自动排队，保持系统稳定
- **会话复用**：智能复用已预热的模型会话，减少加载时间

### 🌐 局域网友好
- **统一入口**：通过5631端口访问所有本地AI服务
- **多用户支持**：支持局域网内多个设备同时访问
- **流式输出**：完整保留deepseek-r1等思维链模型的实时输出体验
- **API兼容**：完全兼容ollama原生API，无需修改客户端代码

### 📊 完整监控
- **健康检查**：实时了解系统运行状态
- **会话管理**：30分钟空闲自动释放，节省内存
- **数据持久化**：任务历史和会话状态完整记录
- **详细日志**：按类型分类的日志文件，便于排查问题

### 🧪 智能测试计划生成
- **自然语言转换**：将测试需求描述转换为结构化执行步骤
- **多平台支持**：智能识别或轮询选择iOS/Android平台
- **完整记录**：保存生成历史，支持搜索和统计分析
- **专业工具集**：内置移动应用测试工具定义和最佳实践

## 🛠️ 快速开始

### 1. 启动服务
```bash
python run.py
```

### 2. 检查服务状态
```bash
curl http://localhost:5631/health
```

### 3. 开始使用
```bash
# 获取可用模型列表
curl http://localhost:5631/ollama/models

# 开始对话
curl -X POST http://localhost:5631/ollama/generate \
  -H "Content-Type: application/json" \
  -d '{"model": "deepseek-r1:32b", "prompt": "你好", "stream": true}'
```

## 📝 API接口详解

### 🔍 健康检查
**获取系统运行状态**

```bash
curl http://localhost:5631/health
```

**响应示例：**
```json
{
  "status": "healthy",
  "services": {
    "ollama": "healthy",
    "memory_monitor": "healthy", 
    "task_queue": "healthy",
    "session_manager": "healthy"
  },
  "system": {
    "memory": {
      "usage_percent": 65.2,
      "under_pressure": false,
      "threshold_percent": 90.0
    },
    "queue": {
      "pending_count": 0,
      "running_count": 2,
      "max_concurrent": 8
    },
    "sessions": {
      "total_sessions": 3,
      "busy_sessions": 2, 
      "idle_sessions": 1
    }
  }
}
```

### 📋 模型管理
**获取可用模型列表**

```bash
curl http://localhost:5631/ollama/models
```

### 💬 文本生成 (Generate)

#### 流式生成（推荐）
**实时获取AI响应，适合交互式场景**

```bash
curl -X POST http://localhost:5631/ollama/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-r1:32b",
    "prompt": "解释一下什么是人工智能",
    "stream": true
  }'
```

**响应格式：**
```json
{"model":"deepseek-r1:32b","response":"<think>","done":false}
{"model":"deepseek-r1:32b","response":"用户问的是人工智能的定义...","done":false}
{"model":"deepseek-r1:32b","response":"</think>","done":false}
{"model":"deepseek-r1:32b","response":"人工智能(AI)是...","done":false}
{"model":"deepseek-r1:32b","response":"","done":true}
```

#### 非流式生成
**一次性获取完整响应**

```bash
curl -X POST http://localhost:5631/ollama/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-r1:32b",
    "prompt": "用一句话总结机器学习",
    "stream": false
  }'
```

### 🧪 移动应用测试计划生成 (Test Plan)

**将自然语言指令转换为结构化测试计划**

#### 生成测试计划
**根据自然语言描述生成移动应用测试步骤**

```bash
curl -X POST http://localhost:5631/test-plan/generate \
  -H "Content-Type: application/json" \
  -d '{
    "instruction": "在iOS设备上打开美团首页，点击搜索框，输入火锅，然后截图",
    "platform": "ios",
    "model": "deepseek-r1:32b"
  }'
```

**请求参数：**
- `instruction` (必需)：自然语言测试指令
- `platform` (可选)：目标平台 `ios` 或 `android`，不指定时自动轮询
- `model` (可选)：使用的模型，默认 `deepseek-r1:32b`
- `stream` (可选)：是否流式响应，暂不支持

**响应示例：**
```json
{
  "status": "success",
  "record_id": "550e8400-e29b-41d4-a716-446655440000",
  "plan": {
    "plan_id": "plan_20250120_143022",
    "original_request": "在iOS设备上打开美团首页，点击搜索框，输入火锅，然后截图",
    "summary": "iOS美团应用搜索火锅并截图的测试流程",
    "platform": "ios",
    "total_steps": 8,
    "steps": [
      {
        "step_id": 1,
        "action": "find_available_device",
        "description": "查找可用的iOS设备",
        "parameters": {"platform": "ios"},
        "parameter_types": {"platform": "static"},
        "parameter_sources": {},
        "expected_result": "成功找到iOS设备"
      },
      {
        "step_id": 2,
        "action": "start_device_test",
        "description": "开始设备测试会话",
        "parameters": {"udid": "{device_udid}"},
        "parameter_types": {"udid": "dynamic"},
        "parameter_sources": {"udid": "来自find_available_device结果"},
        "expected_result": "成功建立测试会话"
      }
    ]
  },
  "processing_time": 2.35,
  "detected_platform": "ios",
  "model_name": "deepseek-r1:32b"
}
```

#### 查看测试计划记录
**获取历史测试计划记录列表**

```bash
# 获取记录列表（支持分页和搜索）
curl "http://localhost:5631/test-plan/records?page=1&page_size=10&search=美团&platform=ios&status=success"

# 获取特定记录详情
curl "http://localhost:5631/test-plan/records/550e8400-e29b-41d4-a716-446655440000"
```

**查询参数：**
- `page`：页码，默认1
- `page_size`：每页数量，默认20，最大100
- `search`：搜索关键词，在指令和摘要中搜索
- `platform`：平台筛选（ios/android）
- `status`：状态筛选（success/failed/error）

#### 获取统计信息
**查看测试计划生成的统计数据**

```bash
curl http://localhost:5631/test-plan/stats
```

**响应示例：**
```json
{
  "total_records": 156,
  "success_records": 142,
  "failed_records": 8,
  "error_records": 6,
  "success_rate": 91.03,
  "platform_distribution": {
    "ios": 78,
    "android": 78
  },
  "recent_activity": [
    {
      "record_id": "550e8400-e29b-41d4-a716-446655440000",
      "instruction": "在iOS设备上打开美团首页，点击搜索框，输入火锅...",
      "platform": "ios",
      "status": "success",
      "created_at": "2025-01-20T14:30:22Z"
    }
  ]
}
```

#### 平台轮询机制
**查看当前平台选择状态**

```bash
curl http://localhost:5631/test-plan/platform-info
```

系统会在 iOS 和 Android 之间自动轮询，确保测试覆盖的平衡性。

### 🗨️ 对话聊天 (Chat)

#### 流式对话
**支持多轮对话历史**

```bash
curl -X POST http://localhost:5631/ollama/chat \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-r1:32b",
    "messages": [
      {"role": "user", "content": "你好，我是小明"},
      {"role": "assistant", "content": "你好小明！很高兴认识你。"},
      {"role": "user", "content": "请问你能帮我做什么？"}
    ],
    "stream": true
  }'
```

#### 系统提示词对话
**使用系统角色设定AI行为**

```bash
curl -X POST http://localhost:5631/ollama/chat \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-r1:32b", 
    "messages": [
      {"role": "system", "content": "你是一个专业的Python编程助手"},
      {"role": "user", "content": "如何读取CSV文件？"}
    ],
    "stream": true
  }'
```

## 🔧 高级配置

### 参数说明
支持ollama原生的所有参数：

```bash
curl -X POST http://localhost:5631/ollama/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-r1:32b",
    "prompt": "写一首关于春天的诗",
    "stream": true,
    "options": {
      "temperature": 0.8,
      "top_p": 0.9,
      "max_tokens": 200
    }
  }'
```

### 配置文件 (config.yaml)
```yaml
# 服务器配置
server:
  host: "0.0.0.0"     # 监听所有网络接口（局域网访问）
  port: 5631          # API服务端口

# 内存管理
memory:
  threshold: 0.90     # 90%内存阈值，超过则启用队列
  check_interval: 5   # 每5秒检查一次内存

# 会话管理  
session:
  max_sessions: 8     # 最大并发ollama会话数
  idle_timeout: 1800  # 30分钟空闲后释放会话
  
# 任务队列
queue:
  max_size: 100       # 最大队列长度
  task_timeout: 3600  # 任务超时时间（1小时）

# 测试计划生成
test_plan:
  default_model: "deepseek-r1:32b"  # 默认使用的模型
  prompts_file: "app/data/prompts/test_plan_prompts.json"  # prompt模板文件
  platform_state_file: "data/.platform_state.json"       # 平台状态文件
```

## 📊 系统监控

### 实时状态查看
```bash
# 查看内存使用情况
curl -s http://localhost:5631/health | jq '.system.memory'

# 查看队列状态  
curl -s http://localhost:5631/health | jq '.system.queue'

# 查看会话状态
curl -s http://localhost:5631/health | jq '.system.sessions'
```

### 日志文件
- `logs/app.log` - 应用主日志
- `logs/api.log` - API访问日志  
- `logs/error.log` - 错误日志

## 🌍 局域网访问设置

### 1. 防火墙配置
确保5631端口在防火墙中开放：

**macOS:**
```bash
# 查看防火墙状态
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --getglobalstate

# 添加Python应用到允许列表
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --add $(which python3)
```

**Linux:**
```bash
# 开放5631端口
sudo ufw allow 5631
```

### 2. 获取服务器IP
```bash
# 查看本机IP地址
ifconfig | grep "inet " | grep -v 127.0.0.1
```

### 3. 局域网访问
其他设备可通过以下方式访问：
```bash
# 替换YOUR_SERVER_IP为实际服务器IP
curl http://YOUR_SERVER_IP:5631/health

# 例如：
curl http://*************:5631/ollama/generate \
  -H "Content-Type: application/json" \
  -d '{"model": "deepseek-r1:32b", "prompt": "你好", "stream": true}'
```

## 🛡️ 智能队列工作原理

### 正常模式（内存 < 90%）
```
用户请求 → 直接转发ollama → 实时响应
```

### 队列模式（内存 ≥ 90%）
```
用户请求 → 进入队列 → 等待空闲会话 → 执行任务 → 返回结果
```

### 会话复用机制
```
新请求 → 检查是否有同模型空闲会话 → 
        ├─ 有：复用会话（快速响应）
        └─ 无：创建新会话（需加载模型）
```

## 🚨 常见问题

### Q: 为什么有时候响应比直接调用ollama慢？
A: 当系统内存超过90%阈值时，会启用队列模式。请求会排队等待，这是为了保护系统稳定性。

### Q: 如何查看当前有多少个任务在排队？
A: 使用健康检查接口：`curl -s http://localhost:5631/health | jq '.system.queue.pending_count'`

### Q: 支持哪些ollama模型？
A: 支持你本地ollama中安装的所有模型。使用 `curl http://localhost:5631/ollama/models` 查看完整列表。

### Q: 如何调整内存阈值？
A: 修改 `config.yaml` 中的 `memory.threshold` 参数（0.0-1.0之间）。

### Q: 会话多久会自动释放？
A: 默认30分钟无活动后自动释放。可在 `config.yaml` 中修改 `session.idle_timeout` 参数。

### Q: 测试计划生成支持哪些平台？
A: 目前支持 iOS 和 Android 平台。不指定平台时系统会自动在两者之间轮询。

### Q: 如何查看测试计划生成的历史记录？
A: 使用 `/test-plan/records` 接口可以查看所有历史记录，支持分页、搜索和筛选。

### Q: 可以自定义测试步骤和工具吗？
A: 可以通过修改 `app/data/prompts/test_plan_prompts.json` 文件来自定义prompt模板和工具定义。

### Q: 平台轮询的顺序是什么？
A: 系统会记住上次使用的平台，下次自动切换到另一个平台，确保iOS和Android的测试覆盖平衡。

## 📈 性能优化建议

1. **内存充足环境**：可将 `memory.threshold` 设置为 0.95，减少不必要的排队
2. **高并发场景**：增加 `session.max_sessions` 值，但需确保内存充足
3. **快速响应**：使用流式输出（`stream: true`），获得更好的用户体验
4. **长期运行**：定期检查 `logs/` 目录，避免日志文件过大

## 🎯 使用场景

### 个人开发
- 本地AI助手，支持多种模型切换
- 代码生成和调试辅助
- 文档写作和翻译
- **移动应用测试计划设计**

### 团队协作  
- 局域网内团队共享AI资源
- 统一的AI服务入口
- 请求负载均衡和排队
- **测试团队协作生成测试用例**

### 服务器部署
- 企业内部AI服务平台
- 多用户并发访问
- 资源使用监控和优化
- **QA团队测试计划标准化生成**

---

**🎉 现在你可以开始使用你的智能AI服务器了！**

如有问题，请查看 `logs/` 目录中的日志文件获取详细错误信息。