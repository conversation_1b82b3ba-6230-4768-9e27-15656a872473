import asyncio
import uuid
import logging
from typing import Dict, Any, Optional, List, Callable, Awaitable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import config
from app.models.database import async_session
from app.models.task import Task, TaskStatus, TaskType
from app.services.memory_monitor import memory_monitor

logger = logging.getLogger(__name__)

@dataclass
class QueuedTask:
    """队列任务数据类"""
    task_id: str
    task_type: TaskType
    model_name: str
    request_data: Dict[str, Any]
    priority: int = 0
    created_at: datetime = None
    timeout_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.timeout_at is None:
            timeout_seconds = config.get('queue.task_timeout', 3600)
            self.timeout_at = self.created_at + timedelta(seconds=timeout_seconds)

class TaskQueueService:
    """任务队列管理服务"""
    
    def __init__(self):
        self.max_size = config.get('queue.max_size', 100)
        self.max_concurrent = config.get('session.max_sessions', 8)
        
        # 任务队列（优先级队列）
        self._pending_queue: List[QueuedTask] = []
        self._running_tasks: Dict[str, QueuedTask] = {}
        self._queue_lock = asyncio.Lock()
        
        # 任务处理器回调
        self._task_handlers: Dict[TaskType, Callable] = {}
        
        # 队列处理任务
        self._processor_task: Optional[asyncio.Task] = None
        self._is_processing = False
    
    def register_handler(self, task_type: TaskType, handler: Callable[[QueuedTask], Awaitable[Any]]):
        """注册任务处理器"""
        self._task_handlers[task_type] = handler
        logger.info(f"注册任务处理器: {task_type.value}")
    
    async def start(self):
        """启动队列处理器"""
        if self._is_processing:
            logger.warning("任务队列处理器已在运行")
            return
        
        self._is_processing = True
        self._processor_task = asyncio.create_task(self._process_queue())
        logger.info("任务队列处理器已启动")
    
    async def stop(self):
        """停止队列处理器"""
        self._is_processing = False
        if self._processor_task:
            self._processor_task.cancel()
            try:
                await self._processor_task
            except asyncio.CancelledError:
                pass
        logger.info("任务队列处理器已停止")
    
    async def enqueue_task(
        self, 
        task_type: TaskType, 
        model_name: str, 
        request_data: Dict[str, Any], 
        priority: int = 0
    ) -> str:
        """入队任务"""
        
        # 检查队列长度
        if len(self._pending_queue) >= self.max_size:
            raise RuntimeError(f"队列已满，最大长度: {self.max_size}")
        
        # 创建任务
        task_id = str(uuid.uuid4())
        queued_task = QueuedTask(
            task_id=task_id,
            task_type=task_type,
            model_name=model_name,
            request_data=request_data,
            priority=priority
        )
        
        async with self._queue_lock:
            # 添加到队列（按优先级排序）
            self._pending_queue.append(queued_task)
            self._pending_queue.sort(key=lambda t: (-t.priority, t.created_at))
        
        # 保存到数据库
        await self._save_task_to_db(queued_task, TaskStatus.QUEUED)
        
        logger.info(f"任务已入队: {task_id}, 类型: {task_type.value}, 模型: {model_name}")
        return task_id
    
    async def _process_queue(self):
        """队列处理循环"""
        while self._is_processing:
            try:
                # 检查是否可以处理新任务
                if (len(self._running_tasks) >= self.max_concurrent or 
                    memory_monitor.is_memory_pressure()):
                    await asyncio.sleep(1)
                    continue
                
                # 获取下一个待处理任务
                next_task = await self._get_next_task()
                if not next_task:
                    await asyncio.sleep(0.5)
                    continue
                
                # 开始处理任务
                await self._start_task_processing(next_task)
                
            except Exception as e:
                logger.error(f"队列处理循环出错: {e}")
                await asyncio.sleep(1)
    
    async def _get_next_task(self) -> Optional[QueuedTask]:
        """获取下一个待处理任务"""
        async with self._queue_lock:
            if not self._pending_queue:
                return None
            
            # 检查任务是否超时
            current_time = datetime.now()
            self._pending_queue = [
                task for task in self._pending_queue 
                if task.timeout_at > current_time
            ]
            
            if not self._pending_queue:
                return None
            
            # 取出优先级最高的任务
            return self._pending_queue.pop(0)
    
    async def _start_task_processing(self, task: QueuedTask):
        """开始处理任务"""
        try:
            # 添加到运行中任务
            self._running_tasks[task.task_id] = task
            
            # 更新数据库状态
            await self._update_task_status(task.task_id, TaskStatus.PROCESSING)
            
            # 获取任务处理器
            handler = self._task_handlers.get(task.task_type)
            if not handler:
                raise ValueError(f"未找到任务类型 {task.task_type.value} 的处理器")
            
            # 异步处理任务
            asyncio.create_task(self._execute_task(task, handler))
            
        except Exception as e:
            logger.error(f"启动任务处理失败: {task.task_id}, 错误: {e}")
            await self._handle_task_error(task, str(e))
    
    async def _execute_task(self, task: QueuedTask, handler: Callable):
        """执行任务"""
        start_time = datetime.now()
        try:
            logger.info(f"开始执行任务: {task.task_id}")
            
            # 执行任务处理器
            result = await handler(task)
            
            # 计算处理时间
            process_time = (datetime.now() - start_time).total_seconds()
            
            # 标记任务完成
            await self._complete_task(task, result, process_time)
            
        except Exception as e:
            logger.error(f"任务执行失败: {task.task_id}, 错误: {e}")
            await self._handle_task_error(task, str(e))
        finally:
            # 从运行中任务移除
            self._running_tasks.pop(task.task_id, None)
    
    async def _complete_task(self, task: QueuedTask, result: Any, process_time: float):
        """完成任务"""
        try:
            # 更新数据库
            await self._update_task_completion(task.task_id, result, process_time)
            
            logger.info(f"任务完成: {task.task_id}, 耗时: {process_time:.2f}s")
            
        except Exception as e:
            logger.error(f"完成任务时出错: {task.task_id}, 错误: {e}")
    
    async def _handle_task_error(self, task: QueuedTask, error_msg: str):
        """处理任务错误"""
        try:
            await self._update_task_status(task.task_id, TaskStatus.FAILED, error_msg)
            logger.error(f"任务失败: {task.task_id}, 错误: {error_msg}")
        except Exception as e:
            logger.error(f"处理任务错误时出错: {e}")
    
    async def _save_task_to_db(self, task: QueuedTask, status: TaskStatus):
        """保存任务到数据库"""
        try:
            async with async_session() as session:
                db_task = Task(
                    task_id=task.task_id,
                    task_type=task.task_type,
                    status=status,
                    model_name=task.model_name,
                    request_data=task.request_data,
                    queue_position=self._get_queue_position(task.task_id),
                    estimated_wait_time=self._estimate_wait_time()
                )
                
                session.add(db_task)
                await session.commit()
                
        except Exception as e:
            logger.error(f"保存任务到数据库失败: {e}")
    
    async def _update_task_status(self, task_id: str, status: TaskStatus, error_msg: str = None):
        """更新任务状态"""
        try:
            async with async_session() as session:
                stmt = (
                    update(Task)
                    .where(Task.task_id == task_id)
                    .values(
                        status=status,
                        error_message=error_msg,
                        started_at=datetime.now() if status == TaskStatus.PROCESSING else None,
                        updated_at=datetime.now()
                    )
                )
                await session.execute(stmt)
                await session.commit()
                
        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")
    
    async def _update_task_completion(self, task_id: str, result: Any, process_time: float):
        """更新任务完成信息"""
        try:
            async with async_session() as session:
                # 提取token统计信息
                total_tokens = 0
                if isinstance(result, dict):
                    total_tokens = result.get('eval_count', 0)
                
                stmt = (
                    update(Task)
                    .where(Task.task_id == task_id)
                    .values(
                        status=TaskStatus.COMPLETED,
                        completed_at=datetime.now(),
                        process_time=process_time,
                        total_tokens=total_tokens,
                        updated_at=datetime.now()
                    )
                )
                await session.execute(stmt)
                await session.commit()
                
        except Exception as e:
            logger.error(f"更新任务完成信息失败: {e}")
    
    def _get_queue_position(self, task_id: str) -> int:
        """获取任务在队列中的位置"""
        for i, task in enumerate(self._pending_queue):
            if task.task_id == task_id:
                return i + 1
        return 0
    
    def _estimate_wait_time(self) -> float:
        """估算等待时间（秒）"""
        # 简单估算：每个任务平均30秒
        avg_task_time = 30
        queue_length = len(self._pending_queue)
        running_count = len(self._running_tasks)
        
        if running_count >= self.max_concurrent:
            return queue_length * avg_task_time / self.max_concurrent
        else:
            return 0
    
    async def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        return {
            'pending_count': len(self._pending_queue),
            'running_count': len(self._running_tasks),
            'max_concurrent': self.max_concurrent,
            'max_queue_size': self.max_size,
            'memory_pressure': memory_monitor.is_memory_pressure(),
            'memory_usage': memory_monitor.get_memory_usage(),
            'is_processing': self._is_processing
        }
    
    async def get_task_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        try:
            async with async_session() as session:
                stmt = select(Task).where(Task.task_id == task_id)
                result = await session.execute(stmt)
                task = result.scalar_one_or_none()
                
                if task:
                    return task.to_dict()
                return None
                
        except Exception as e:
            logger.error(f"获取任务信息失败: {e}")
            return None
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            # 从队列中移除
            async with self._queue_lock:
                self._pending_queue = [
                    task for task in self._pending_queue 
                    if task.task_id != task_id
                ]
            
            # 更新数据库状态
            await self._update_task_status(task_id, TaskStatus.CANCELLED)
            
            logger.info(f"任务已取消: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            return False

# 全局任务队列服务实例
task_queue = TaskQueueService()