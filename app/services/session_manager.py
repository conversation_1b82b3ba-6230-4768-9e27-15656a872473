import asyncio
import uuid
import logging
import psutil
import subprocess
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import config
from app.models.database import get_db_session
from app.models.session import OllamaSession, SessionStatus
from app.services.memory_monitor import memory_monitor

logger = logging.getLogger(__name__)

class SessionManagerService:
    """ollama会话窗口管理服务"""
    
    def __init__(self):
        self.max_sessions = config.get('session.max_sessions', 8)
        self.idle_timeout = config.get('session.idle_timeout', 1800)  # 30分钟
        self.cleanup_interval = config.get('session.cleanup_interval', 300)  # 5分钟
        
        # 会话跟踪
        self._active_sessions: Dict[str, Dict[str, Any]] = {}
        self._model_sessions: Dict[str, List[str]] = {}  # model_name -> [session_ids]
        
        # 后台任务
        self._cleanup_task: Optional[asyncio.Task] = None
        self._is_managing = False
    
    async def start(self):
        """启动会话管理器"""
        if self._is_managing:
            logger.warning("会话管理器已在运行")
            return
        
        self._is_managing = True
        
        # 从数据库恢复会话状态
        await self._restore_sessions_from_db()
        
        # 启动清理任务
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        logger.info(f"会话管理器已启动，最大会话数: {self.max_sessions}，空闲超时: {self.idle_timeout}s")
    
    async def stop(self):
        """停止会话管理器"""
        self._is_managing = False
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 清理所有活跃会话
        await self._cleanup_all_sessions()
        
        logger.info("会话管理器已停止")
    
    async def get_or_create_session(self, model_name: str, priority: int = 0) -> Optional[str]:
        """获取或创建会话"""
        
        # 1. 首先尝试复用同模型的空闲会话
        session_id = await self._find_idle_session(model_name)
        if session_id:
            await self._activate_session(session_id)
            logger.info(f"复用空闲会话: {session_id} for model {model_name}")
            return session_id
        
        # 2. 检查是否可以创建新会话
        if len(self._active_sessions) >= self.max_sessions:
            # 尝试清理空闲会话
            await self._cleanup_idle_sessions()
            
            if len(self._active_sessions) >= self.max_sessions:
                logger.warning(f"已达到最大会话数限制: {self.max_sessions}")
                return None
        
        # 3. 检查内存压力
        if memory_monitor.is_memory_pressure():
            logger.warning("内存压力过大，无法创建新会话")
            return None
        
        # 4. 创建新会话
        session_id = await self._create_new_session(model_name)
        return session_id
    
    async def _find_idle_session(self, model_name: str) -> Optional[str]:
        """查找空闲的同模型会话"""
        model_sessions = self._model_sessions.get(model_name, [])
        
        for session_id in model_sessions:
            session_info = self._active_sessions.get(session_id)
            if session_info and session_info['status'] == SessionStatus.IDLE:
                # 检查会话是否还有效
                if await self._is_session_valid(session_id):
                    return session_id
                else:
                    # 会话无效，移除
                    await self._remove_session(session_id)
        
        return None
    
    async def _create_new_session(self, model_name: str) -> str:
        """创建新的ollama会话"""
        session_id = str(uuid.uuid4())
        
        try:
            # 创建会话信息
            session_info = {
                'session_id': session_id,
                'model_name': model_name,
                'status': SessionStatus.LOADING,
                'created_at': datetime.now(),
                'last_used_at': datetime.now(),
                'process_id': None,
                'memory_usage': 0.0,
                'total_requests': 0
            }
            
            # 添加到活跃会话
            self._active_sessions[session_id] = session_info
            
            # 添加到模型会话映射
            if model_name not in self._model_sessions:
                self._model_sessions[model_name] = []
            self._model_sessions[model_name].append(session_id)
            
            # 保存到数据库
            await self._save_session_to_db(session_info)
            
            # 模拟模型预热（实际场景中可能需要预加载模型）
            await asyncio.sleep(0.1)  # 模拟加载时间
            
            # 更新状态为空闲
            session_info['status'] = SessionStatus.IDLE
            await self._update_session_status(session_id, SessionStatus.IDLE)
            
            logger.info(f"创建新会话: {session_id} for model {model_name}")
            return session_id
            
        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            # 清理失败的会话
            self._active_sessions.pop(session_id, None)
            if model_name in self._model_sessions:
                try:
                    self._model_sessions[model_name].remove(session_id)
                except ValueError:
                    pass
            raise
    
    async def _activate_session(self, session_id: str):
        """激活会话（标记为忙碌状态）"""
        session_info = self._active_sessions.get(session_id)
        if session_info:
            session_info['status'] = SessionStatus.BUSY
            session_info['last_used_at'] = datetime.now()
            session_info['total_requests'] += 1
            
            await self._update_session_status(session_id, SessionStatus.BUSY)
    
    async def release_session(self, session_id: str, success: bool = True):
        """释放会话（标记为空闲状态）"""
        session_info = self._active_sessions.get(session_id)
        if session_info:
            session_info['status'] = SessionStatus.IDLE
            session_info['last_used_at'] = datetime.now()
            
            await self._update_session_status(session_id, SessionStatus.IDLE)
            logger.debug(f"会话已释放: {session_id}")
    
    async def _cleanup_loop(self):
        """清理循环"""
        while self._is_managing:
            try:
                await self._cleanup_idle_sessions()
                await self._cleanup_error_sessions()
                await self._update_memory_usage()
                
                await asyncio.sleep(self.cleanup_interval)
                
            except Exception as e:
                logger.error(f"清理循环出错: {e}")
                await asyncio.sleep(self.cleanup_interval)
    
    async def _cleanup_idle_sessions(self):
        """清理空闲超时的会话"""
        current_time = datetime.now()
        sessions_to_remove = []
        
        for session_id, session_info in self._active_sessions.items():
            if session_info['status'] == SessionStatus.IDLE:
                idle_time = (current_time - session_info['last_used_at']).total_seconds()
                
                if idle_time > self.idle_timeout:
                    sessions_to_remove.append(session_id)
        
        for session_id in sessions_to_remove:
            await self._remove_session(session_id)
            logger.info(f"清理空闲会话: {session_id}")
    
    async def _cleanup_error_sessions(self):
        """清理错误状态的会话"""
        sessions_to_remove = []
        
        for session_id, session_info in self._active_sessions.items():
            if session_info['status'] == SessionStatus.ERROR:
                sessions_to_remove.append(session_id)
            elif not await self._is_session_valid(session_id):
                sessions_to_remove.append(session_id)
        
        for session_id in sessions_to_remove:
            await self._remove_session(session_id)
            logger.info(f"清理错误会话: {session_id}")
    
    async def _remove_session(self, session_id: str):
        """移除会话"""
        session_info = self._active_sessions.pop(session_id, None)
        if session_info:
            model_name = session_info['model_name']
            
            # 从模型会话映射中移除
            if model_name in self._model_sessions:
                try:
                    self._model_sessions[model_name].remove(session_id)
                    if not self._model_sessions[model_name]:
                        del self._model_sessions[model_name]
                except ValueError:
                    pass
            
            # 更新数据库状态
            await self._update_session_status(session_id, SessionStatus.TERMINATED)
            
            logger.debug(f"会话已移除: {session_id}")
    
    async def _cleanup_all_sessions(self):
        """清理所有活跃会话"""
        session_ids = list(self._active_sessions.keys())
        for session_id in session_ids:
            await self._remove_session(session_id)
    
    async def _is_session_valid(self, session_id: str) -> bool:
        """检查会话是否有效"""
        session_info = self._active_sessions.get(session_id)
        if not session_info:
            return False
        
        # 检查进程是否还存在（如果有进程ID）
        process_id = session_info.get('process_id')
        if process_id:
            try:
                return psutil.pid_exists(process_id)
            except Exception:
                return False
        
        return True
    
    async def _update_memory_usage(self):
        """更新会话内存使用情况"""
        try:
            # 获取当前系统内存使用
            memory_stats = memory_monitor.get_current_stats()
            if not memory_stats:
                return
            
            # 简单估算：每个活跃会话占用的内存
            active_count = len([s for s in self._active_sessions.values() 
                              if s['status'] in [SessionStatus.BUSY, SessionStatus.LOADING]])
            
            if active_count > 0:
                avg_memory_per_session = memory_stats['used_memory'] / max(active_count, 1)
                
                for session_info in self._active_sessions.values():
                    if session_info['status'] in [SessionStatus.BUSY, SessionStatus.LOADING]:
                        session_info['memory_usage'] = avg_memory_per_session
                    else:
                        session_info['memory_usage'] = 0.1  # 空闲会话的基础内存占用
                        
        except Exception as e:
            logger.error(f"更新内存使用失败: {e}")
    
    async def _save_session_to_db(self, session_info: Dict[str, Any]):
        """保存会话到数据库"""
        try:
            async with async_session() as session:
                db_session = OllamaSession(
                    session_id=session_info['session_id'],
                    model_name=session_info['model_name'],
                    status=session_info['status'],
                    process_id=session_info.get('process_id'),
                    memory_usage=session_info.get('memory_usage', 0.0),
                    total_requests=session_info.get('total_requests', 0)
                )
                
                session.add(db_session)
                await session.commit()
                
        except Exception as e:
            logger.error(f"保存会话到数据库失败: {e}")
    
    async def _update_session_status(self, session_id: str, status: SessionStatus):
        """更新会话状态到数据库"""
        try:
            async with async_session() as session:
                stmt = (
                    update(OllamaSession)
                    .where(OllamaSession.session_id == session_id)
                    .values(
                        status=status,
                        last_used_at=datetime.now(),
                        last_heartbeat=datetime.now()
                    )
                )
                await session.execute(stmt)
                await session.commit()
                
        except Exception as e:
            logger.error(f"更新会话状态失败: {e}")
    
    async def _restore_sessions_from_db(self):
        """从数据库恢复会话状态"""
        try:
            async for session in get_db_session():
                # 查询最近的活跃会话
                cutoff_time = datetime.now() - timedelta(hours=1)
                stmt = (
                    select(OllamaSession)
                    .where(
                        OllamaSession.last_used_at >= cutoff_time,
                        OllamaSession.status.in_([SessionStatus.IDLE, SessionStatus.BUSY])
                    )
                )

                result = await session.execute(stmt)
                db_sessions = result.scalars().all()

                # 恢复会话状态
                for db_session in db_sessions:
                    if await self._is_session_valid(db_session.session_id):
                        session_info = {
                            'session_id': db_session.session_id,
                            'model_name': db_session.model_name,
                            'status': SessionStatus.IDLE,  # 重启后都标记为空闲
                            'created_at': db_session.created_at or datetime.now(),
                            'last_used_at': db_session.last_used_at or datetime.now(),
                            'process_id': db_session.process_id,
                            'memory_usage': db_session.memory_usage or 0.0,
                            'total_requests': db_session.total_requests or 0
                        }

                        self._active_sessions[db_session.session_id] = session_info

                        # 添加到模型映射
                        model_name = db_session.model_name
                        if model_name not in self._model_sessions:
                            self._model_sessions[model_name] = []
                        self._model_sessions[model_name].append(db_session.session_id)

                logger.info(f"从数据库恢复了 {len(db_sessions)} 个会话")
                break  # 只需要执行一次
                
        except Exception as e:
            logger.error(f"从数据库恢复会话失败: {e}")
    
    async def get_session_stats(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        active_count = len(self._active_sessions)
        busy_count = len([s for s in self._active_sessions.values() 
                         if s['status'] == SessionStatus.BUSY])
        idle_count = len([s for s in self._active_sessions.values() 
                         if s['status'] == SessionStatus.IDLE])
        
        model_stats = {}
        for model_name, session_ids in self._model_sessions.items():
            model_stats[model_name] = len(session_ids)
        
        return {
            'total_sessions': active_count,
            'busy_sessions': busy_count,
            'idle_sessions': idle_count,
            'max_sessions': self.max_sessions,
            'idle_timeout_seconds': self.idle_timeout,
            'model_distribution': model_stats,
            'memory_usage_estimate': sum(
                s.get('memory_usage', 0) for s in self._active_sessions.values()
            )
        }
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取指定会话信息"""
        return self._active_sessions.get(session_id)
    
    async def force_cleanup_session(self, session_id: str) -> bool:
        """强制清理指定会话"""
        if session_id in self._active_sessions:
            await self._remove_session(session_id)
            return True
        return False

# 全局会话管理器实例
session_manager = SessionManagerService()