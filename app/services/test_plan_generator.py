#!/usr/bin/env python3
"""
测试计划生成器服务
基于自然语言指令生成结构化的移动应用测试计划
"""

import json
import re
import os
import time
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import logging
from pathlib import Path

from app.config import config
from app.services.ollama_proxy import ollama_proxy

logger = logging.getLogger(__name__)

class TestPlanGenerator:
    """测试计划生成器服务类"""
    
    # 平台状态文件路径
    _platform_state_file = "data/.platform_state.json"
    
    def __init__(self):
        """初始化测试计划生成器"""
        self.default_model = config.get('test_plan.default_model', 'deepseek-r1:32b')
        self.prompts_file = config.get('test_plan.prompts_file', 'app/data/prompts/test_plan_prompts.json')
        
        # 处理平台状态文件路径（确保相对于项目根目录）
        state_file_config = config.get('test_plan.platform_state_file', 'data/.platform_state.json')
        if os.path.isabs(state_file_config):
            self._platform_state_file = state_file_config
        else:
            # 相对于项目根目录
            base_dir = Path(__file__).parent.parent.parent  # 项目根目录
            self._platform_state_file = str(base_dir / state_file_config)
        
        # 工具定义映射
        self.tool_mapping = self._create_tool_mapping()
        
        # 加载prompt模板
        self.prompt_templates = self._load_prompt_templates()
        
        # 确保平台状态文件目录存在
        self._ensure_platform_state_dir()
        
        logger.info(f"测试计划生成器初始化成功，默认模型: {self.default_model}")
    
    def _ensure_platform_state_dir(self):
        """确保平台状态文件目录存在"""
        try:
            state_dir = os.path.dirname(self._platform_state_file)
            os.makedirs(state_dir, exist_ok=True)
        except Exception as e:
            logger.warning(f"创建平台状态文件目录失败: {e}")
    
    def _load_prompt_templates(self) -> Dict[str, Any]:
        """加载prompt模板文件"""
        try:
            # 支持相对路径和绝对路径
            if os.path.isabs(self.prompts_file):
                prompts_path = self.prompts_file
            else:
                # 相对于项目根目录的路径
                base_dir = Path(__file__).parent.parent.parent  # 项目根目录
                prompts_path = base_dir / self.prompts_file
            
            logger.info(f"尝试加载prompt模板: {prompts_path}")
            
            with open(prompts_path, 'r', encoding='utf-8') as f:
                templates = json.load(f)
            
            logger.info("prompt模板加载成功")
            return templates
        except Exception as e:
            logger.error(f"加载prompt模板失败: {e} (路径: {prompts_path if 'prompts_path' in locals() else self.prompts_file})")
            return {}
    
    def _create_tool_mapping(self) -> Dict[str, Dict]:
        """创建工具映射表"""
        return {
            "find_available_device": {
                "description": "查找可用的测试设备",
                "required_params": ["platform"],
                "optional_params": [],
                "example": {"platform": "ios"}
            },
            "ocr_text_validation": {
                "description": "通过OCR搜索包含指定文本的元素信息，返回元素位置和详细信息",
                "required_params": ["udid", "target_text"],
                "optional_params": [],
                "example": {"udid": "{device_udid}", "target_text": "外卖"}
            },
            "start_device_test": {
                "description": "开始设备测试会话",
                "required_params": ["udid"],
                "optional_params": [],
                "example": {"udid": "{device_udid}"}
            },
            "take_screenshot": {
                "description": "对设备进行截图",
                "required_params": ["udid"],
                "optional_params": ["description"],
                "example": {"udid": "{device_udid}", "description": "首页截图"}
            },
            "tap_device": {
                "description": "点击屏幕指定位置",
                "required_params": ["udid", "x", "y"],
                "optional_params": ["description"],
                "example": {"udid": "{device_udid}", "x": 100, "y": 200, "description": "点击搜索框"}
            },
            "slide_device": {
                "description": "滑动屏幕",
                "required_params": ["udid", "from_x", "from_y", "to_x", "to_y"],
                "optional_params": ["duration", "description"],
                "example": {"udid": "{device_udid}", "from_x": 0.5, "from_y": 0.8, "to_x": 0.5, "to_y": 0.2, "duration": 0.5, "description": "向上滑动"}
            },
            "input_text_smart": {
                "description": "在输入框中输入文字",
                "required_params": ["udid", "text"],
                "optional_params": ["element_index"],
                "example": {"udid": "{device_udid}", "text": "北京", "element_index": 0}
            },
            "wait_seconds": {
                "description": "等待指定秒数",
                "required_params": ["seconds"],
                "optional_params": [],
                "example": {"seconds": 3}
            },
            "check_page_display": {
                "description": "检查页面是否存在UI bug或显示异常",
                "required_params": ["udid"],
                "optional_params": ["scene_desc"],
                "example": {"udid": "{device_udid}", "scene_desc": "检查首页UI bug"}
            },
            "find_element_on_page": {
                "description": "从页面layout信息和截图中查找指定元素的位置和详细信息",
                "required_params": ["udid", "element"],
                "optional_params": ["scene_desc"],
                "example": {"udid": "{device_udid}", "element": "搜索框", "scene_desc": "从首页布局中定位搜索框"}
            },
            "ocr_text_only": {
                "description": "返回当前页面的OCR结果，可用于判断当前页面的展示内容",
                "required_params": ["udid"],
                "optional_params": [],
                "example": {"udid": "{device_udid}"}
            },
            "restart_application": {
                "description": "重启应用",
                "required_params": ["udid"],
                "optional_params": [],
                "example": {"udid": "{device_udid}"}
            },
            "end_device_test": {
                "description": "结束设备测试会话",
                "required_params": ["udid"],
                "optional_params": [],
                "example": {"udid": "{device_udid}"}
            },
            "analyze_meituan_page": {
                "description": "分析美团app界面截图，识别当前界面特征和界面类型",
                "required_params": ["udid"],
                "optional_params": ["action_description", "model"],
                "example": {"udid": "{device_udid}", "action_description": "点击搜索框后", "model": "qwen2.5vl:7b"}
            }
        }
    
    def _detect_platform_from_text(self, text: str) -> Optional[str]:
        """从自然语言中检测平台"""
        text_lower = text.lower()
        
        # iOS关键词
        ios_keywords = ['ios', 'iphone', 'ipad', '苹果', 'apple', 'ios设备']
        # Android关键词  
        android_keywords = ['android', '安卓', 'google', 'android设备']
        
        ios_count = sum(1 for keyword in ios_keywords if keyword in text_lower)
        android_count = sum(1 for keyword in android_keywords if keyword in text_lower)
        
        if ios_count > android_count:
            return "ios"
        elif android_count > ios_count:
            return "android"
        else:
            return None
    
    def _load_platform_state(self) -> str:
        """从文件中加载上次使用的平台状态"""
        try:
            if os.path.exists(self._platform_state_file):
                with open(self._platform_state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get("last_platform", "android")
            else:
                return "android"  # 默认值
        except Exception as e:
            logger.warning(f"读取平台状态文件失败: {e}")
            return "android"
    
    def _save_platform_state(self, platform: str) -> None:
        """保存当前平台状态到文件"""
        try:
            data = {"last_platform": platform}
            with open(self._platform_state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"保存平台状态文件失败: {e}")
    
    def _get_next_platform(self) -> str:
        """获取下一个平台（轮询方式）"""
        # 读取上次使用的平台
        last_platform = self._load_platform_state()
        
        # 轮询到下一个平台
        if last_platform == "ios":
            next_platform = "android"
        else:
            next_platform = "ios"
        
        # 保存新的平台状态
        self._save_platform_state(next_platform)
        
        return next_platform
    
    def determine_platform(self, natural_language: str, specified_platform: Optional[str] = None) -> str:
        """确定目标平台"""
        # 1. 如果明确指定了平台，直接使用
        if specified_platform and specified_platform.lower() in ["ios", "android"]:
            platform = specified_platform.lower()
            logger.info(f"使用指定平台: {platform.upper()}")
            return platform
        
        # 2. 尝试从自然语言中检测平台
        detected_platform = self._detect_platform_from_text(natural_language)
        if detected_platform:
            logger.info(f"从文本中检测到平台: {detected_platform.upper()}")
            return detected_platform
        
        # 3. 使用轮询机制
        next_platform = self._get_next_platform()
        logger.info(f"轮询选择平台: {next_platform.upper()}")
        return next_platform
    
    def _create_system_prompt(self) -> str:
        """创建系统提示词"""
        if not self.prompt_templates:
            return self._create_system_prompt_fallback()
        
        try:
            template = self.prompt_templates.get("system_prompt_template", {})
            
            # 构建工具信息
            tools_info = []
            for action, info in self.tool_mapping.items():
                tools_info.append(f"- {action}: {info['description']}")
                tools_info.append(f"  必需参数: {info['required_params']}")
                tools_info.append(f"  可选参数: {info['optional_params']}")
                tools_info.append(f"  示例: {json.dumps(info['example'], ensure_ascii=False)}")
                tools_info.append("")
            tools_text = "\\n".join(tools_info)
            
            # 构建系统提示词的各个部分
            prompt_parts = []
            
            # 角色描述
            prompt_parts.append(template.get("role_description", ""))
            prompt_parts.append("")
            
            # 可用操作类型
            available_ops = template.get("available_operations", {})
            prompt_parts.append(available_ops.get("header", ""))
            prompt_parts.append(tools_text)
            prompt_parts.append("")
            
            # 测试规则
            test_rules = template.get("test_rules", {})
            prompt_parts.append(test_rules.get("header", ""))
            prompt_parts.append("")
            
            # 各个子规则
            for rule_key in ["session_management", "coordinate_system", "page_check_tools", 
                           "text_recognition", "text_input_process", "page_state_analysis"]:
                rule_section = test_rules.get(rule_key, {})
                if rule_section:
                    prompt_parts.append(rule_section.get("title", ""))
                    for rule in rule_section.get("rules", []):
                        prompt_parts.append(rule)
                    prompt_parts.append("")
            
            # 重要规则
            important_rules = template.get("important_rules", {})
            if important_rules:
                prompt_parts.append(important_rules.get("header", ""))
                for rule in important_rules.get("rules", []):
                    prompt_parts.append(rule)
                prompt_parts.append("")
            
            # 输出格式
            output_format = template.get("output_format", {})
            if output_format:
                prompt_parts.append(output_format.get("header", ""))
                prompt_parts.append(output_format.get("description", ""))
                prompt_parts.append("")
                
                # JSON模板
                json_template = output_format.get("json_template", {})
                if json_template:
                    json_str = json.dumps(json_template, ensure_ascii=False, indent=2)
                    prompt_parts.append(json_str)
                    prompt_parts.append("")
            
            # 参数类型说明
            param_types = template.get("parameter_types", {})
            if param_types:
                prompt_parts.append(param_types.get("header", ""))
                for rule in param_types.get("rules", []):
                    prompt_parts.append(rule)
                prompt_parts.append("")
            
            # 参数来源说明
            param_sources = template.get("parameter_sources", {})
            if param_sources:
                prompt_parts.append(param_sources.get("header", ""))
                for rule in param_sources.get("rules", []):
                    prompt_parts.append(rule)
                prompt_parts.append("")
            
            # 示例
            examples = template.get("examples", {})
            if examples:
                prompt_parts.append(examples.get("header", ""))
                prompt_parts.append(examples.get("example_input", ""))
                prompt_parts.append("")
                
                for example in examples.get("example_steps", []):
                    prompt_parts.append(example.get("description", ""))
                    example_json = example.get("json_example", {})
                    if example_json:
                        json_str = json.dumps(example_json, ensure_ascii=False, indent=2)
                        prompt_parts.append("```json")
                        prompt_parts.append(json_str)
                        prompt_parts.append("```")
                    prompt_parts.append("")
                
                # 关键区别
                key_diff = examples.get("key_differences", {})
                if key_diff:
                    prompt_parts.append(key_diff.get("header", ""))
                    for rule in key_diff.get("rules", []):
                        prompt_parts.append(rule)
                    prompt_parts.append("")
            
            # 最终指令
            final_instruction = template.get("final_instruction", "")
            if final_instruction:
                prompt_parts.append(final_instruction)
            
            return "\\n".join(prompt_parts)
            
        except Exception as e:
            logger.error(f"使用JSON模板构建prompt失败: {e}")
            return self._create_system_prompt_fallback()
    
    def _create_system_prompt_fallback(self) -> str:
        """fallback系统提示词"""
        tools_info = []
        for action, info in self.tool_mapping.items():
            tools_info.append(f"- {action}: {info['description']}")
            tools_info.append(f"  必需参数: {info['required_params']}")
            tools_info.append(f"  可选参数: {info['optional_params']}")
            tools_info.append(f"  示例: {json.dumps(info['example'], ensure_ascii=False)}")
            tools_info.append("")
        
        tools_text = "\\n".join(tools_info)
        
        return f"""你是一个专业的移动应用测试计划生成器。你的任务是将用户提供的自然语言测试用例转换为结构化的JSON执行计划。

**可用的操作类型：**
{tools_text}

请严格按照JSON格式输出，确保可以被Python的json.loads()正确解析。"""
    
    async def _generate_plan_stream(self, request_data: Dict[str, Any], task_id: str, 
                                  target_platform: str, natural_language_request: str, 
                                  model_name: str, start_time: float):
        """流式生成测试计划"""
        accumulated_response = ""
        
        try:
            # 调用流式ollama代理
            async for chunk in ollama_proxy.generate_stream(request_data, task_id):
                chunk_data = json.loads(chunk)
                
                if 'response' in chunk_data:
                    response_chunk = chunk_data['response']
                    accumulated_response += response_chunk
                    
                    # 发送实时进度
                    progress_data = {
                        "type": "progress",
                        "chunk": response_chunk,
                        "accumulated_length": len(accumulated_response),
                        "task_id": task_id
                    }
                    yield f"data: {json.dumps(progress_data, ensure_ascii=False)}\n\n"
                
                # 检查是否完成
                if chunk_data.get('done', False):
                    logger.info(f"流式生成完成，总长度: {len(accumulated_response)}")
                    
                    # 处理最终响应
                    processing_time = time.time() - start_time
                    json_data = self._extract_json_from_response(accumulated_response)
                    
                    if json_data:
                        json_data["platform"] = target_platform
                        
                        final_result = {
                            "type": "final",
                            "status": "success",
                            "plan": json_data,
                            "processing_time": processing_time,
                            "detected_platform": target_platform,
                            "model_name": model_name,
                            "response_length": len(accumulated_response),
                            "prompt_tokens": self._estimate_token_count(request_data.get('prompt', ''))
                        }
                    else:
                        final_result = {
                            "type": "final",
                            "status": "error",
                            "error": "未找到有效的JSON格式",
                            "raw_response": accumulated_response,
                            "processing_time": processing_time,
                            "detected_platform": target_platform,
                            "model_name": model_name
                        }
                    
                    yield f"data: {json.dumps(final_result, ensure_ascii=False)}\n\n"
                    break
                    
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"流式生成测试计划时发生异常: {str(e)}")
            
            error_result = {
                "type": "final",
                "status": "error",
                "error": f"生成计划时发生异常: {str(e)}",
                "processing_time": processing_time,
                "detected_platform": target_platform
            }
            yield f"data: {json.dumps(error_result, ensure_ascii=False)}\n\n"

    async def generate_plan(self, natural_language_request: str, platform: Optional[str] = None, 
                          model: Optional[str] = None, stream: bool = False) -> Dict[str, Any]:
        """生成测试计划"""
        start_time = time.time()
        
        try:
            logger.info(f"开始生成测试计划: {natural_language_request[:50]}...")
            
            # 确定目标平台
            target_platform = self.determine_platform(natural_language_request, platform)
            
            # 确定使用的模型
            model_name = model or self.default_model
            
            # 验证模型是否存在
            if not await ollama_proxy.validate_model(model_name):
                logger.error(f"模型 {model_name} 不存在")
                return {
                    "status": "error",
                    "error": f"模型 {model_name} 不存在",
                    "processing_time": time.time() - start_time,
                    "detected_platform": target_platform
                }
            
            # 构建prompt
            system_prompt = self._create_system_prompt()
            
            # 构建用户提示词
            user_prompt_template = self.prompt_templates.get("user_prompt_template", 
                "请将以下测试用例转换为结构化执行计划:\\n\\n目标平台: {target_platform}\\n测试用例: {natural_language_request}")
            user_prompt = user_prompt_template.format(
                target_platform=target_platform.upper(),
                natural_language_request=natural_language_request
            )
            
            # 构建请求参数
            request_data = {
                "model": model_name,
                "prompt": f"System: {system_prompt}\\n\\nUser: {user_prompt}",
                "stream": stream,
                "raw": False,
                "options": {
                    "temperature": 0.1
                }
            }
            
            logger.info(f"调用模型 {model_name} 进行计划生成")
            logger.debug(f"系统提示词长度: {len(system_prompt)} 字符")
            logger.debug(f"用户提示词: {user_prompt}")
            
            # 调用ollama代理
            if stream:
                # 流式响应支持
                task_id = str(uuid.uuid4())
                return {
                    "status": "success",
                    "stream": True,
                    "task_id": task_id,
                    "stream_generator": self._generate_plan_stream(request_data, task_id, target_platform, natural_language_request, model_name, start_time),
                    "detected_platform": target_platform,
                    "model_name": model_name
                }
            else:
                # 非流式响应
                task_id = str(uuid.uuid4())
                result = await ollama_proxy.generate_sync(request_data, task_id)
                
                if not result or 'response' not in result:
                    return {
                        "status": "error",
                        "error": "模型调用失败，未返回有效响应",
                        "processing_time": time.time() - start_time,
                        "detected_platform": target_platform
                    }
                
                response_text = result.get('response', '').strip()
                
                processing_time = time.time() - start_time
                logger.info(f"模型调用完成，耗时: {processing_time:.2f}秒")
                logger.debug(f"模型原始响应: {response_text[:200]}...")
                
                # 提取JSON
                json_data = self._extract_json_from_response(response_text)
                
                if json_data:
                    # 确保平台信息正确
                    json_data["platform"] = target_platform
                    
                    logger.info(f"JSON解析成功，生成了 {json_data.get('total_steps', 0)} 个步骤")
                    
                    return {
                        "status": "success",
                        "plan": json_data,
                        "raw_response": response_text,
                        "processing_time": processing_time,
                        "detected_platform": target_platform,
                        "model_name": model_name,
                        "response_length": len(response_text),
                        "prompt_tokens": self._estimate_token_count(system_prompt + user_prompt)
                    }
                else:
                    logger.error("未找到有效的JSON格式")
                    return {
                        "status": "error",
                        "error": "未找到有效的JSON格式",
                        "raw_response": response_text,
                        "processing_time": processing_time,
                        "detected_platform": target_platform,
                        "model_name": model_name
                    }
                    
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"生成计划时发生异常: {str(e)}")
            return {
                "status": "error",
                "error": f"生成计划时发生异常: {str(e)}",
                "processing_time": processing_time,
                "detected_platform": locals().get('target_platform', 'unknown')
            }
    
    def _extract_json_from_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """从响应中提取JSON"""
        # 清理思维链标签
        cleaned_response = response_text
        think_pattern = r'<think>.*?</think>'
        cleaned_response = re.sub(think_pattern, '', cleaned_response, flags=re.DOTALL)
        
        # 策略1: 查找代码块中的JSON（优先）
        code_block_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
        matches = re.findall(code_block_pattern, cleaned_response, re.DOTALL | re.IGNORECASE)
        for match in matches:
            try:
                cleaned_json = self._clean_json_string(match)
                data = json.loads(cleaned_json)
                if self._validate_plan_structure(data):
                    return data
            except json.JSONDecodeError:
                continue
        
        # 策略2: 查找完整的JSON对象
        json_patterns = [
            r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # 简单嵌套
            r'\{.*?\}',  # 最小匹配
            r'\{.*\}',   # 最大匹配
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, cleaned_response, re.DOTALL)
            for match in matches:
                try:
                    cleaned_json = self._clean_json_string(match)
                    data = json.loads(cleaned_json)
                    
                    if self._validate_plan_structure(data):
                        return data
                except json.JSONDecodeError:
                    continue
        
        return None
    
    def _clean_json_string(self, json_str: str) -> str:
        """清理JSON字符串中的常见问题"""
        # 移除前后空白
        json_str = json_str.strip()
        
        # 修复常见的格式问题
        json_str = re.sub(r',\s*}', '}', json_str)  # 移除尾随逗号
        json_str = re.sub(r',\s*]', ']', json_str)  # 移除数组尾随逗号
        
        try:
            # 先尝试直接解析
            data = json.loads(json_str)
            
            # 修复 total_steps 计数问题
            if 'steps' in data and 'total_steps' in data:
                actual_steps = len(data['steps'])
                if data['total_steps'] != actual_steps:
                    data['total_steps'] = actual_steps
            
            # 重新序列化
            return json.dumps(data, ensure_ascii=False, separators=(',', ':'))
        except json.JSONDecodeError:
            # 如果解析失败，尝试修复常见问题
            # 确保字符串值被正确引用（但要小心已经引用的值）
            json_str = re.sub(r':\s*([^",\[\]{}]+)\s*([,}])', r': "\1"\2', json_str)
            
            return json_str
    
    def _validate_plan_structure(self, data: Dict[str, Any]) -> bool:
        """验证计划结构是否有效"""
        required_fields = ['plan_id', 'steps', 'total_steps']
        
        for field in required_fields:
            if field not in data:
                return False
        
        if not isinstance(data['steps'], list):
            return False
            
        if len(data['steps']) == 0:
            return False
            
        # 验证步骤结构
        for step in data['steps']:
            if not isinstance(step, dict):
                return False
            step_required = ['step_id', 'action', 'description', 'parameters']
            for field in step_required:
                if field not in step:
                    return False
        
        return True
    
    def _estimate_token_count(self, text: str) -> int:
        """估算token数量（粗略估算）"""
        # 简单估算：1个token ≈ 4个字符（对中文来说）
        return len(text) // 4
    
    def get_platform_stats(self) -> Dict[str, Any]:
        """获取平台统计信息"""
        try:
            current_platform = self._load_platform_state()
            return {
                "current_platform": current_platform,
                "next_platform": "ios" if current_platform == "android" else "android",
                "platform_state_file": self._platform_state_file
            }
        except Exception as e:
            logger.error(f"获取平台统计失败: {e}")
            return {"error": str(e)}
    
    def get_prompt_info(self) -> Dict[str, Any]:
        """获取prompt模板信息"""
        try:
            # 返回加载的prompt模板信息
            if self.prompt_templates:
                return self.prompt_templates
            else:
                # 如果模板未加载，尝试重新加载
                self.prompt_templates = self._load_prompt_templates()
                return self.prompt_templates
        except Exception as e:
            logger.error(f"获取prompt信息失败: {e}")
            return {
                "error": f"无法加载prompt信息: {str(e)}",
                "prompts_file": self.prompts_file
            }
    
    async def get_model_info(self) -> Dict[str, Any]:
        """获取当前模型信息"""
        try:
            from app.services.ollama_proxy import ollama_proxy
            
            # 获取可用模型列表
            available_models = await ollama_proxy.get_models()
            
            # 查找当前默认模型的详细信息
            current_model_info = None
            for model in available_models.get('models', []):
                if model.get('name') == self.default_model:
                    current_model_info = model
                    break
            
            return {
                "current_model": self.default_model,
                "model_details": current_model_info,
                "available_models_count": len(available_models.get('models', [])),
                "prompts_file": self.prompts_file,
                "platform_state_file": self._platform_state_file,
                "last_updated": time.time()
            }
        except Exception as e:
            logger.error(f"获取模型信息失败: {e}")
            return {
                "current_model": self.default_model,
                "error": f"无法获取详细模型信息: {str(e)}",
                "prompts_file": self.prompts_file,
                "last_updated": time.time()
            }

# 全局实例
test_plan_generator = TestPlanGenerator()