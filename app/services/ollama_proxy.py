import httpx
import asyncio
import json
import logging
from typing import AsyncIterator, Dict, Any, Optional
from fastapi import HTTPException
from fastapi.responses import StreamingResponse

from app.config import config

logger = logging.getLogger(__name__)

class OllamaProxyService:
    """Ollama代理服务"""
    
    def __init__(self):
        self.base_url = config.ollama_base_url
        self.timeout = config.get('ollama.timeout', 300)
        self.max_connections = config.get('ollama.max_connections', 10)
        
        # HTTP客户端配置，忽略系统代理
        self.http_client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout, connect=30.0),
            limits=httpx.Limits(max_connections=self.max_connections),
            follow_redirects=True,
            proxies={}  # 显式设置为空，忽略系统代理
        )
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def close(self):
        """关闭HTTP客户端"""
        await self.http_client.aclose()
    
    async def check_health(self) -> bool:
        """检查ollama服务健康状态"""
        try:
            response = await self.http_client.get(f"{self.base_url}/api/tags")
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Ollama健康检查失败: {e}")
            return False
    
    async def get_models(self) -> Dict[str, Any]:
        """获取可用模型列表"""
        try:
            response = await self.http_client.get(f"{self.base_url}/api/tags")
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            logger.error(f"获取模型列表失败: {e}")
            raise HTTPException(status_code=503, detail="无法连接到Ollama服务")
        except Exception as e:
            logger.error(f"获取模型列表时发生未知错误: {e}")
            raise HTTPException(status_code=500, detail="服务器内部错误")
    
    async def validate_model(self, model_name: str) -> bool:
        """验证模型是否存在"""
        try:
            models_data = await self.get_models()
            available_models = [model['name'] for model in models_data.get('models', [])]
            return model_name in available_models
        except Exception:
            return False
    
    async def generate_stream(
        self, 
        request_data: Dict[str, Any],
        task_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> AsyncIterator[bytes]:
        """流式generate请求代理"""
        
        # 确保stream参数为true
        request_data['stream'] = True
        
        try:
            logger.info(f"开始流式generate请求 - 模型: {request_data.get('model')}, 任务ID: {task_id}, 会话ID: {session_id}")
            
            async with self.http_client.stream(
                "POST",
                f"{self.base_url}/api/generate", 
                json=request_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                
                if response.status_code != 200:
                    error_msg = f"Ollama服务返回错误状态码: {response.status_code}"
                    logger.error(error_msg)
                    # 返回错误响应格式
                    error_response = {
                        "model": request_data.get('model', ''),
                        "done": True,
                        "error": error_msg
                    }
                    yield f"{json.dumps(error_response)}\n".encode('utf-8')
                    return
                
                async for chunk in response.aiter_lines():
                    if chunk.strip():  # 跳过空行
                        try:
                            # 验证JSON格式
                            json.loads(chunk)
                            yield f"{chunk}\n".encode('utf-8')
                        except json.JSONDecodeError:
                            logger.warning(f"接收到无效JSON块: {chunk}")
                            continue
                    
        except httpx.TimeoutException:
            logger.error("Ollama请求超时")
            error_response = {
                "model": request_data.get('model', ''),
                "done": True,
                "error": "请求超时"
            }
            yield f"{json.dumps(error_response)}\n".encode('utf-8')
            
        except httpx.RequestError as e:
            logger.error(f"Ollama连接错误: {e}")
            error_response = {
                "model": request_data.get('model', ''),
                "done": True, 
                "error": f"连接错误: {str(e)}"
            }
            yield f"{json.dumps(error_response)}\n".encode('utf-8')
            
        except Exception as e:
            logger.error(f"流式generate请求发生未知错误: {e}")
            error_response = {
                "model": request_data.get('model', ''),
                "done": True,
                "error": f"服务器错误: {str(e)}"
            }
            yield f"{json.dumps(error_response)}\n".encode('utf-8')
    
    async def generate_sync(
        self, 
        request_data: Dict[str, Any],
        task_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """非流式generate请求代理"""
        
        # 确保stream参数为false
        request_data['stream'] = False
        
        try:
            logger.info(f"开始非流式generate请求 - 模型: {request_data.get('model')}, 任务ID: {task_id}, 会话ID: {session_id}")
            
            response = await self.http_client.post(
                f"{self.base_url}/api/generate",
                json=request_data,
                headers={'Content-Type': 'application/json'}
            )
            
            response.raise_for_status()
            result = response.json()
            
            logger.info(f"非流式generate请求完成 - 任务ID: {task_id}")
            return result
            
        except httpx.HTTPError as e:
            logger.error(f"非流式generate请求失败: {e}")
            raise HTTPException(status_code=response.status_code if 'response' in locals() else 503, 
                              detail=f"Ollama服务错误: {str(e)}")
        except Exception as e:
            logger.error(f"非流式generate请求发生未知错误: {e}")
            raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")
    
    async def chat_stream(
        self, 
        request_data: Dict[str, Any],
        task_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> AsyncIterator[bytes]:
        """流式chat请求代理"""
        
        # 确保stream参数为true
        request_data['stream'] = True
        
        try:
            logger.info(f"开始流式chat请求 - 模型: {request_data.get('model')}, 任务ID: {task_id}")
            
            async with self.http_client.stream(
                "POST",
                f"{self.base_url}/api/chat",
                json=request_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                
                if response.status_code != 200:
                    error_msg = f"Ollama服务返回错误状态码: {response.status_code}"
                    logger.error(error_msg)
                    error_response = {
                        "model": request_data.get('model', ''),
                        "done": True,
                        "error": error_msg
                    }
                    yield f"{json.dumps(error_response)}\n".encode('utf-8')
                    return
                
                async for chunk in response.aiter_lines():
                    if chunk.strip():
                        try:
                            json.loads(chunk)
                            yield f"{chunk}\n".encode('utf-8')
                        except json.JSONDecodeError:
                            logger.warning(f"接收到无效JSON块: {chunk}")
                            continue
                    
        except Exception as e:
            logger.error(f"流式chat请求发生错误: {e}")
            error_response = {
                "model": request_data.get('model', ''),
                "done": True,
                "error": str(e)
            }
            yield f"{json.dumps(error_response)}\n".encode('utf-8')
    
    async def chat_sync(
        self, 
        request_data: Dict[str, Any],
        task_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """非流式chat请求代理"""
        
        # 确保stream参数为false
        request_data['stream'] = False
        
        try:
            logger.info(f"开始非流式chat请求 - 模型: {request_data.get('model')}, 任务ID: {task_id}")
            
            response = await self.http_client.post(
                f"{self.base_url}/api/chat",
                json=request_data,
                headers={'Content-Type': 'application/json'}
            )
            
            response.raise_for_status()
            result = response.json()
            
            logger.info(f"非流式chat请求完成 - 任务ID: {task_id}")
            return result
            
        except httpx.HTTPError as e:
            logger.error(f"非流式chat请求失败: {e}")
            raise HTTPException(status_code=response.status_code if 'response' in locals() else 503,
                              detail=f"Ollama服务错误: {str(e)}")
        except Exception as e:
            logger.error(f"非流式chat请求发生未知错误: {e}")
            raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

# 全局代理服务实例
ollama_proxy = OllamaProxyService()