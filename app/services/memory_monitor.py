import asyncio
import psutil
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import config
from app.models.database import async_session
from app.models.task import MemoryStat

logger = logging.getLogger(__name__)

class MemoryMonitorService:
    """内存监控服务"""
    
    def __init__(self):
        self.threshold = config.memory_threshold
        self.check_interval = config.get('memory.check_interval', 5)
        self.is_monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None
        self._current_stats: Optional[Dict[str, Any]] = None
        
    async def start(self):
        """启动内存监控"""
        if self.is_monitoring:
            logger.warning("内存监控已经在运行中")
            return
        
        self.is_monitoring = True
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info(f"内存监控服务已启动，阈值: {self.threshold*100}%，检查间隔: {self.check_interval}s")
    
    async def stop(self):
        """停止内存监控"""
        self.is_monitoring = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("内存监控服务已停止")
    
    async def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 获取系统内存信息
                memory_stats = self._get_memory_stats()
                
                # 更新当前统计
                self._current_stats = memory_stats
                
                # 保存到数据库
                await self._save_memory_stats(memory_stats)
                
                # 检查内存压力
                if memory_stats['memory_percent'] >= self.threshold:
                    logger.warning(
                        f"内存使用率超过阈值: {memory_stats['memory_percent']:.1f}% >= {self.threshold*100}%"
                    )
                
                # 等待下次检查
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"内存监控循环出错: {e}")
                await asyncio.sleep(self.check_interval)
    
    def _get_memory_stats(self) -> Dict[str, Any]:
        """获取系统内存统计信息"""
        try:
            # 获取内存信息
            memory = psutil.virtual_memory()
            
            # 获取CPU信息
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # macOS更准确的内存使用率计算
            # 不包括inactive内存，因为它可以被快速释放
            if hasattr(memory, 'active') and hasattr(memory, 'wired'):
                # macOS特定计算：只算active和wired内存为真正使用
                actual_used = memory.active + memory.wired
                actual_percent = (actual_used / memory.total) * 100
            else:
                # 其他系统使用标准计算
                actual_used = memory.used
                actual_percent = memory.percent
            
            stats = {
                'total_memory': memory.total / (1024**3),  # GB
                'available_memory': memory.available / (1024**3),  # GB
                'used_memory': actual_used / (1024**3),  # GB
                'memory_percent': actual_percent / 100,  # 转换为0-1之间的值
                'cpu_percent': cpu_percent,
                'timestamp': datetime.now(),
                # 添加详细信息用于调试
                'memory_details': {
                    'active': getattr(memory, 'active', 0) / (1024**3),
                    'inactive': getattr(memory, 'inactive', 0) / (1024**3),
                    'wired': getattr(memory, 'wired', 0) / (1024**3),
                    'free': getattr(memory, 'free', 0) / (1024**3),
                    'psutil_percent': memory.percent,
                    'adjusted_percent': actual_percent
                }
            }
            
            logger.debug(f"内存统计: 调整后 {actual_percent:.1f}%, psutil原始 {memory.percent:.1f}%")
            return stats
            
        except Exception as e:
            logger.error(f"获取内存统计信息失败: {e}")
            return {
                'total_memory': 0.0,
                'available_memory': 0.0,
                'used_memory': 0.0,
                'memory_percent': 0.0,
                'cpu_percent': 0.0,
                'timestamp': datetime.now()
            }
    
    async def _save_memory_stats(self, stats: Dict[str, Any]):
        """保存内存统计到数据库"""
        try:
            # 检查async_session是否已初始化
            if async_session is None:
                logger.debug("数据库会话未初始化，跳过内存统计保存")
                return
                
            async with async_session() as session:
                memory_stat = MemoryStat(
                    total_memory=stats['total_memory'],
                    available_memory=stats['available_memory'],
                    used_memory=stats['used_memory'],
                    memory_percent=stats['memory_percent'],
                    cpu_percent=stats['cpu_percent'],
                    active_sessions=0,  # 稍后从会话管理器获取
                    queue_length=0  # 稍后从任务队列获取
                )
                
                session.add(memory_stat)
                await session.commit()
                
        except Exception as e:
            logger.error(f"保存内存统计失败: {e}")
    
    def get_current_stats(self) -> Optional[Dict[str, Any]]:
        """获取当前内存统计"""
        return self._current_stats
    
    def is_memory_pressure(self) -> bool:
        """检查是否存在内存压力"""
        if not self._current_stats:
            return False
        return self._current_stats['memory_percent'] >= self.threshold
    
    def get_memory_usage(self) -> float:
        """获取当前内存使用百分比"""
        if not self._current_stats:
            memory = psutil.virtual_memory()
            return memory.percent / 100
        return self._current_stats['memory_percent']
    
    async def get_memory_history(
        self, 
        session: AsyncSession, 
        hours: int = 24
    ) -> list:
        """获取内存使用历史"""
        try:
            # 计算查询时间范围
            since = datetime.now() - timedelta(hours=hours)
            
            # 查询最近的内存统计
            stmt = (
                select(MemoryStat)
                .where(MemoryStat.timestamp >= since)
                .order_by(MemoryStat.timestamp.desc())
                .limit(1000)
            )
            
            result = await session.execute(stmt)
            stats = result.scalars().all()
            
            return [stat.to_dict() for stat in stats]
            
        except Exception as e:
            logger.error(f"获取内存历史失败: {e}")
            return []
    
    async def cleanup_old_stats(self, session: AsyncSession, days: int = 7):
        """清理旧的内存统计数据"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # 删除过期数据
            stmt = select(MemoryStat).where(MemoryStat.timestamp < cutoff_date)
            result = await session.execute(stmt)
            old_stats = result.scalars().all()
            
            for stat in old_stats:
                await session.delete(stat)
            
            await session.commit()
            logger.info(f"清理了 {len(old_stats)} 条过期内存统计数据")
            
        except Exception as e:
            logger.error(f"清理内存统计数据失败: {e}")
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            # 内存信息
            memory = psutil.virtual_memory()
            
            # CPU信息
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            
            return {
                'memory': {
                    'total': memory.total / (1024**3),
                    'available': memory.available / (1024**3),
                    'used': memory.used / (1024**3),
                    'percent': memory.percent
                },
                'cpu': {
                    'count': cpu_count,
                    'frequency': {
                        'current': cpu_freq.current if cpu_freq else None,
                        'min': cpu_freq.min if cpu_freq else None,
                        'max': cpu_freq.max if cpu_freq else None
                    }
                },
                'disk': {
                    'total': disk.total / (1024**3),
                    'used': disk.used / (1024**3),
                    'free': disk.free / (1024**3),
                    'percent': (disk.used / disk.total) * 100
                }
            }
        except Exception as e:
            logger.error(f"获取系统信息失败: {e}")
            return {}

# 全局内存监控服务实例
memory_monitor = MemoryMonitorService()