import yaml
import os
from typing import Dict, Any
from pathlib import Path

class Config:
    """配置管理类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        # 确保配置文件路径相对于项目根目录
        if not os.path.isabs(config_path):
            # 获取项目根目录（config.py的上上级目录）
            project_root = Path(__file__).parent.parent
            self.config_path = project_root / config_path
        else:
            self.config_path = config_path
        self._config: Dict[str, Any] = {}
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f) or {}
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件 {self.config_path} 未找到")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置项，支持点号分隔的嵌套key"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    @property
    def server_host(self) -> str:
        return self.get('server.host', '0.0.0.0')
    
    @property
    def server_port(self) -> int:
        return self.get('server.port', 5631)
    
    @property
    def ollama_base_url(self) -> str:
        return self.get('ollama.base_url', 'http://localhost:11434')
    
    @property
    def memory_threshold(self) -> float:
        return self.get('memory.threshold', 0.90)
    
    @property
    def database_url(self) -> str:
        return self.get('database.url', 'sqlite+aiosqlite:///./data/database/app.db')
    
    def ensure_directories(self) -> None:
        """确保必要的目录存在"""
        directories = [
            'logs',
            'data/database', 
            'data/cache'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

# 全局配置实例
config = Config()