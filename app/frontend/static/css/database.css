/* 数据库管理页面专用样式 */

/* 头部操作区域 */
.header-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* 概览卡片 */
.overview-section {
    margin-bottom: 30px;
}

.overview-section h2 {
    color: white;
    margin-bottom: 15px;
    text-align: center;
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.overview-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    cursor: pointer;
}

.overview-card h3 {
    margin: 0 0 10px 0;
    color: #667eea;
    font-size: 1.1rem;
}

.overview-card .card-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.overview-card .total-count {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

.overview-card .recent-count {
    font-size: 0.9rem;
    color: #666;
}

/* 表格工具栏 */
.table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid #e9ecef;
    flex-wrap: wrap;
    gap: 15px;
}

.toolbar-left {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.toolbar-left h3 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
}

.toolbar-left .record-count {
    color: #666;
    font-size: 0.9rem;
}

.toolbar-right {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.search-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    width: 200px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    background: white;
}

/* 数据表格 */
.table-container {
    background: white;
    border-radius: 0 0 8px 8px;
    overflow-x: auto;
    min-height: 400px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.data-table .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: flex-start;
    flex-wrap: wrap;
}

.data-table .btn-small {
    padding: 6px 12px;
    font-size: 0.85rem;
    border-radius: 4px;
    min-width: 60px;
    text-align: center;
    white-space: nowrap;
}

/* 让表格行可点击 */
.data-table tbody tr {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.data-table tbody tr:hover {
    background: #f0f8ff !important;
}

/* 详情按钮样式 */
.btn-view {
    background: #17a2b8;
    color: white;
    border: 1px solid #17a2b8;
}

.btn-view:hover {
    background: #138496;
    border-color: #117a8b;
}

/* 只读表单样式 */
.form-group input[readonly],
.form-group textarea[readonly],
.form-group select[disabled] {
    background-color: #f8f9fa;
    border-color: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
}

.form-group textarea[readonly] {
    resize: none;
}

/* 改善表格行的悬停效果 */
.data-table tbody tr:hover .action-buttons {
    opacity: 1;
}

.data-table .action-buttons {
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

/* 状态标签 */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-idle { background: #d4edda; color: #155724; }
.status-busy { background: #fff3cd; color: #856404; }
.status-loading { background: #cce5ff; color: #004085; }
.status-error { background: #f8d7da; color: #721c24; }
.status-terminated { background: #e2e3e5; color: #383d41; }
.status-pending { background: #e2e3e5; color: #383d41; }
.status-queued { background: #cce5ff; color: #004085; }
.status-processing { background: #fff3cd; color: #856404; }
.status-completed { background: #d4edda; color: #155724; }
.status-failed { background: #f8d7da; color: #721c24; }
.status-cancelled { background: #e2e3e5; color: #383d41; }
.status-success { background: #d4edda; color: #155724; }

/* 分页控件 */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 0 0 8px 8px;
    border-top: 1px solid #e9ecef;
}

.page-info {
    color: #666;
    font-size: 0.9rem;
}

.page-size-select {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    background: white;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e9ecef;
}

/* 表单样式 */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-group .form-help {
    font-size: 0.8rem;
    color: #666;
    margin-top: 3px;
}

/* 提示消息 */
.error-toast,
.success-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 4px;
    color: white;
    z-index: 1001;
    display: flex;
    align-items: center;
    gap: 10px;
    max-width: 400px;
}

.error-toast {
    background: #dc3545;
}

.success-toast {
    background: #28a745;
}

.toast-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: auto;
}

/* 加载状态 */
.table-loading,
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    color: #666;
    font-size: 1.1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .table-toolbar {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .toolbar-right {
        flex-wrap: wrap;
    }
    
    .search-input {
        width: 100%;
    }
    
    .overview-cards {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}
