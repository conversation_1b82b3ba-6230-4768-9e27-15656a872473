// 数据库管理页面JavaScript

class DatabaseManager {
    constructor() {
        this.currentTable = 'ollama_sessions';
        this.currentPage = 1;
        this.pageSize = 20;
        this.searchQuery = '';
        this.statusFilter = '';
        this.isEditing = false;
        this.editingId = null;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadOverview();
        this.loadTableData();
    }
    
    bindEvents() {
        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTable(e.target.dataset.table);
            });
        });
        
        // 搜索和筛选
        document.getElementById('search-input').addEventListener('input', (e) => {
            this.searchQuery = e.target.value;
            this.currentPage = 1;
            this.loadTableData();
        });
        
        document.getElementById('status-filter').addEventListener('change', (e) => {
            this.statusFilter = e.target.value;
            this.currentPage = 1;
            this.loadTableData();
        });
        
        // 分页控件
        document.getElementById('prev-page').addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.loadTableData();
            }
        });
        
        document.getElementById('next-page').addEventListener('click', () => {
            this.currentPage++;
            this.loadTableData();
        });
        
        document.getElementById('page-size-select').addEventListener('change', (e) => {
            this.pageSize = parseInt(e.target.value);
            this.currentPage = 1;
            this.loadTableData();
        });
        
        // 刷新按钮
        document.getElementById('refresh-overview').addEventListener('click', () => {
            this.loadOverview();
        });
        
        document.getElementById('refresh-table-btn').addEventListener('click', () => {
            this.loadTableData();
        });
        
        // 新增记录
        document.getElementById('add-record-btn').addEventListener('click', () => {
            this.showRecordModal();
        });
        
        // 模态框控制
        document.getElementById('modal-close').addEventListener('click', () => {
            this.hideRecordModal();
        });
        
        document.getElementById('cancel-modal').addEventListener('click', () => {
            this.hideRecordModal();
        });
        
        document.getElementById('save-record').addEventListener('click', () => {
            this.saveRecord();
        });
        
        document.getElementById('delete-record').addEventListener('click', () => {
            this.showConfirmModal();
        });
        
        // 确认删除模态框
        document.getElementById('confirm-modal-close').addEventListener('click', () => {
            this.hideConfirmModal();
        });
        
        document.getElementById('cancel-delete').addEventListener('click', () => {
            this.hideConfirmModal();
        });
        
        document.getElementById('confirm-delete').addEventListener('click', () => {
            this.deleteRecord();
        });
        
        // 提示消息关闭
        document.getElementById('error-close').addEventListener('click', () => {
            document.getElementById('error-toast').style.display = 'none';
        });
        
        document.getElementById('success-close').addEventListener('click', () => {
            document.getElementById('success-toast').style.display = 'none';
        });
    }
    
    async loadOverview() {
        try {
            const response = await fetch('/database/overview');
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.detail || '获取概览失败');
            }
            
            this.renderOverview(data);
        } catch (error) {
            this.showError('加载数据库概览失败: ' + error.message);
        }
    }
    
    renderOverview(data) {
        const container = document.getElementById('overview-cards');
        const tableNames = {
            'ollama_sessions': '🔗 会话管理',
            'tasks': '📋 任务队列',
            'memory_stats': '📈 内存统计',
            'test_plan_records': '🧪 测试计划'
        };

        container.innerHTML = data.tables.map(table => `
            <div class="overview-card" onclick="dbManager.switchTable('${table.table_name}')" style="cursor: pointer;">
                <h3>${tableNames[table.table_name] || table.table_name}</h3>
                <div class="card-stats">
                    <span class="total-count">${table.total_records}</span>
                    <span class="recent-count">最近24h: ${table.recent_records}</span>
                </div>
            </div>
        `).join('');
    }
    
    switchTable(tableName) {
        // 更新活跃标签
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-table="${tableName}"]`).classList.add('active');
        
        // 更新当前表
        this.currentTable = tableName;
        this.currentPage = 1;
        this.searchQuery = '';
        this.statusFilter = '';
        
        // 重置搜索和筛选
        document.getElementById('search-input').value = '';
        document.getElementById('status-filter').value = '';
        
        // 更新表标题
        const titles = {
            'ollama_sessions': '会话管理',
            'tasks': '任务队列',
            'memory_stats': '内存统计', 
            'test_plan_records': '测试计划'
        };
        document.getElementById('table-title').textContent = titles[tableName];
        
        // 更新状态筛选选项
        this.updateStatusFilter();
        
        // 加载新表数据
        this.loadTableData();
    }
    
    updateStatusFilter() {
        const statusOptions = {
            'ollama_sessions': [
                {value: 'idle', text: '空闲'},
                {value: 'busy', text: '忙碌'},
                {value: 'loading', text: '加载中'},
                {value: 'error', text: '错误'},
                {value: 'terminated', text: '已终止'}
            ],
            'tasks': [
                {value: 'pending', text: '待处理'},
                {value: 'queued', text: '队列中'},
                {value: 'processing', text: '处理中'},
                {value: 'completed', text: '已完成'},
                {value: 'failed', text: '失败'},
                {value: 'cancelled', text: '已取消'}
            ],
            'memory_stats': [],
            'test_plan_records': [
                {value: 'success', text: '成功'},
                {value: 'failed', text: '失败'},
                {value: 'error', text: '错误'}
            ]
        };
        
        const select = document.getElementById('status-filter');
        select.innerHTML = '<option value="">所有状态</option>';
        
        const options = statusOptions[this.currentTable] || [];
        options.forEach(option => {
            select.innerHTML += `<option value="${option.value}">${option.text}</option>`;
        });
    }
    
    async loadTableData() {
        const loading = document.getElementById('table-loading');
        const table = document.getElementById('data-table');
        const pagination = document.getElementById('pagination');
        
        loading.style.display = 'flex';
        table.style.display = 'none';
        pagination.style.display = 'none';
        
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                page_size: this.pageSize
            });
            
            if (this.searchQuery) params.append('search', this.searchQuery);
            if (this.statusFilter) params.append('status', this.statusFilter);
            
            const endpoint = this.getTableEndpoint();
            const response = await fetch(`${endpoint}?${params}`);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.detail || '获取数据失败');
            }
            
            this.renderTable(data);
            this.renderPagination(data);
            
            loading.style.display = 'none';
            table.style.display = 'table';
            pagination.style.display = 'flex';
            
        } catch (error) {
            loading.style.display = 'none';
            this.showError('加载表格数据失败: ' + error.message);
        }
    }
    
    getTableEndpoint() {
        const endpoints = {
            'ollama_sessions': '/database/sessions',
            'tasks': '/database/tasks',
            'memory_stats': '/database/memory-stats',
            'test_plan_records': '/database/test-plan-records'
        };
        return endpoints[this.currentTable];
    }
    
    renderTable(data) {
        const thead = document.getElementById('table-head');
        const tbody = document.getElementById('table-body');
        const recordCount = document.getElementById('record-count');
        
        recordCount.textContent = `总计: ${data.total} 条记录`;
        
        if (data.data.length === 0) {
            thead.innerHTML = '';
            tbody.innerHTML = '<tr><td colspan="100%" style="text-align: center; padding: 40px; color: #666;">暂无数据</td></tr>';
            return;
        }
        
        // 生成表头
        const firstRow = data.data[0];
        const headers = this.getTableHeaders(firstRow);
        thead.innerHTML = `
            <tr>
                ${headers.map(header => `<th>${header.label}</th>`).join('')}
                <th>操作</th>
            </tr>
        `;
        
        // 生成表格内容
        tbody.innerHTML = data.data.map(row => `
            <tr onclick="dbManager.viewRecord('${this.getRecordId(row)}')" style="cursor: pointer;">
                ${headers.map(header => `<td>${this.formatCellValue(row[header.key], header.type)}</td>`).join('')}
                <td onclick="event.stopPropagation();">
                    <div class="action-buttons">
                        <button class="btn btn-small btn-view" onclick="dbManager.viewRecord('${this.getRecordId(row)}')">详情</button>
                        <button class="btn btn-small btn-primary" onclick="dbManager.editRecord('${this.getRecordId(row)}')">编辑</button>
                        <button class="btn btn-small btn-danger" onclick="dbManager.confirmDelete('${this.getRecordId(row)}')">删除</button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    getTableHeaders(row) {
        const headerConfigs = {
            'ollama_sessions': [
                {key: 'session_id', label: '会话ID', type: 'text'},
                {key: 'model_name', label: '模型名称', type: 'text'},
                {key: 'status', label: '状态', type: 'status'},
                {key: 'memory_usage', label: '内存使用(GB)', type: 'number'},
                {key: 'total_requests', label: '总请求数', type: 'number'},
                {key: 'created_at', label: '创建时间', type: 'datetime'}
            ],
            'tasks': [
                {key: 'task_id', label: '任务ID', type: 'text'},
                {key: 'task_type', label: '任务类型', type: 'text'},
                {key: 'status', label: '状态', type: 'status'},
                {key: 'model_name', label: '模型名称', type: 'text'},
                {key: 'process_time', label: '处理时间(s)', type: 'number'},
                {key: 'created_at', label: '创建时间', type: 'datetime'}
            ],
            'memory_stats': [
                {key: 'timestamp', label: '时间戳', type: 'datetime'},
                {key: 'memory_percent', label: '内存使用率(%)', type: 'number'},
                {key: 'used_memory', label: '已用内存(GB)', type: 'number'},
                {key: 'available_memory', label: '可用内存(GB)', type: 'number'},
                {key: 'cpu_percent', label: 'CPU使用率(%)', type: 'number'},
                {key: 'active_sessions', label: '活跃会话数', type: 'number'}
            ],
            'test_plan_records': [
                {key: 'record_id', label: '记录ID', type: 'text'},
                {key: 'original_instruction', label: '原始指令', type: 'text'},
                {key: 'detected_platform', label: '平台', type: 'text'},
                {key: 'status', label: '状态', type: 'status'},
                {key: 'processing_time', label: '处理时间(s)', type: 'number'},
                {key: 'created_at', label: '创建时间', type: 'datetime'}
            ]
        };
        
        return headerConfigs[this.currentTable] || Object.keys(row).map(key => ({key, label: key, type: 'text'}));
    }
    
    formatCellValue(value, type) {
        if (value === null || value === undefined) return '-';
        
        switch (type) {
            case 'datetime':
                return new Date(value).toLocaleString('zh-CN');
            case 'number':
                return typeof value === 'number' ? value.toFixed(2) : value;
            case 'status':
                return `<span class="status-badge status-${value}">${value}</span>`;
            case 'text':
            default:
                if (typeof value === 'string' && value.length > 50) {
                    return value.substring(0, 50) + '...';
                }
                return value;
        }
    }
    
    getRecordId(row) {
        const idFields = {
            'ollama_sessions': 'session_id',
            'tasks': 'task_id', 
            'memory_stats': 'id',
            'test_plan_records': 'record_id'
        };
        return row[idFields[this.currentTable]];
    }
    
    renderPagination(data) {
        const pageInfo = document.getElementById('page-info');
        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');
        
        pageInfo.textContent = `第 ${data.page} 页，共 ${data.total_pages} 页`;
        
        prevBtn.disabled = data.page <= 1;
        nextBtn.disabled = data.page >= data.total_pages;
    }
    
    showError(message) {
        const toast = document.getElementById('error-toast');
        const messageEl = document.getElementById('error-message');
        messageEl.textContent = message;
        toast.style.display = 'flex';
        
        setTimeout(() => {
            toast.style.display = 'none';
        }, 5000);
    }
    
    showSuccess(message) {
        const toast = document.getElementById('success-toast');
        const messageEl = document.getElementById('success-message');
        messageEl.textContent = message;
        toast.style.display = 'flex';
        
        setTimeout(() => {
            toast.style.display = 'none';
        }, 3000);
    }
    
    showRecordModal(recordId = null, viewOnly = false) {
        this.isEditing = !!recordId && !viewOnly;
        this.isViewOnly = viewOnly;
        this.editingId = recordId;

        const modal = document.getElementById('record-modal');
        const title = document.getElementById('modal-title');
        const deleteBtn = document.getElementById('delete-record');
        const saveBtn = document.getElementById('save-record');

        if (viewOnly) {
            title.textContent = '查看记录详情';
            deleteBtn.style.display = 'none';
            saveBtn.style.display = 'none';
        } else {
            title.textContent = this.isEditing ? '编辑记录' : '新增记录';
            deleteBtn.style.display = this.isEditing ? 'inline-block' : 'none';
            saveBtn.style.display = 'inline-block';
        }

        this.generateForm(recordId, viewOnly);
        modal.style.display = 'block';
    }
    
    hideRecordModal() {
        document.getElementById('record-modal').style.display = 'none';
    }
    
    showConfirmModal() {
        document.getElementById('confirm-modal').style.display = 'block';
    }
    
    hideConfirmModal() {
        document.getElementById('confirm-modal').style.display = 'none';
    }
    
    async viewRecord(recordId) {
        this.showRecordModal(recordId, true); // 第二个参数表示只读模式

        if (recordId) {
            try {
                const endpoint = this.getTableEndpoint();
                const response = await fetch(`${endpoint}/${recordId}`);
                const data = await response.json();

                if (response.ok) {
                    this.populateForm(data, true); // 第二个参数表示只读模式
                } else {
                    this.showError('获取记录详情失败');
                }
            } catch (error) {
                this.showError('获取记录详情失败: ' + error.message);
            }
        }
    }

    async editRecord(recordId) {
        this.showRecordModal(recordId);

        if (recordId) {
            try {
                const endpoint = this.getTableEndpoint();
                const response = await fetch(`${endpoint}/${recordId}`);
                const data = await response.json();

                if (response.ok) {
                    this.populateForm(data);
                } else {
                    this.showError('获取记录详情失败');
                }
            } catch (error) {
                this.showError('获取记录详情失败: ' + error.message);
            }
        }
    }
    
    confirmDelete(recordId) {
        this.editingId = recordId;
        this.showConfirmModal();
    }
    
    generateForm(recordId, viewOnly = false) {
        const container = document.getElementById('form-fields');
        const formConfigs = this.getFormConfig();
        const readonlyAttr = viewOnly ? 'readonly disabled' : '';

        container.innerHTML = formConfigs.map(field => {
            let inputHtml = '';

            switch (field.type) {
                case 'select':
                    inputHtml = `
                        <select id="${field.key}" name="${field.key}" ${field.required ? 'required' : ''} ${viewOnly ? 'disabled' : ''}>
                            ${field.options.map(opt => `<option value="${opt.value}">${opt.text}</option>`).join('')}
                        </select>
                    `;
                    break;
                case 'textarea':
                    inputHtml = `<textarea id="${field.key}" name="${field.key}" ${field.required ? 'required' : ''} ${readonlyAttr}></textarea>`;
                    break;
                case 'number':
                    inputHtml = `<input type="number" id="${field.key}" name="${field.key}" step="0.01" ${field.required ? 'required' : ''} ${readonlyAttr}>`;
                    break;
                case 'datetime-local':
                    inputHtml = `<input type="datetime-local" id="${field.key}" name="${field.key}" ${field.required ? 'required' : ''} ${readonlyAttr}>`;
                    break;
                default:
                    inputHtml = `<input type="text" id="${field.key}" name="${field.key}" ${field.required ? 'required' : ''} ${readonlyAttr}>`;
            }

            return `
                <div class="form-group">
                    <label for="${field.key}">${field.label}</label>
                    ${inputHtml}
                    ${field.help && !viewOnly ? `<div class="form-help">${field.help}</div>` : ''}
                </div>
            `;
        }).join('');
    }

    getFormConfig() {
        const configs = {
            'ollama_sessions': [
                {key: 'model_name', label: '模型名称', type: 'text', required: true, help: '要使用的模型名称'},
                {key: 'process_id', label: '进程ID', type: 'number', required: false, help: 'Ollama进程ID（可选）'},
                {key: 'port', label: '端口', type: 'number', required: false, help: '独立端口（可选）'},
                {key: 'memory_usage', label: '内存使用量(GB)', type: 'number', required: false, help: '会话内存使用量'},
                {key: 'model_size', label: '模型大小(GB)', type: 'number', required: false, help: '模型文件大小'},
                {key: 'max_context_length', label: '最大上下文长度', type: 'number', required: false, help: '模型支持的最大上下文长度'},
                {key: 'temperature', label: '温度参数', type: 'number', required: false, help: '生成文本的随机性参数(0-1)'}
            ],
            'tasks': [
                {key: 'task_type', label: '任务类型', type: 'select', required: true,
                 options: [
                     {value: 'generate', text: '文本生成'},
                     {value: 'chat', text: '对话'},
                     {value: 'test_plan', text: '测试计划'}
                 ]},
                {key: 'model_name', label: '模型名称', type: 'text', required: true, help: '要使用的模型名称'},
                {key: 'session_id', label: '会话ID', type: 'text', required: false, help: '关联的会话ID（可选）'}
            ],
            'memory_stats': [
                {key: 'total_memory', label: '总内存(GB)', type: 'number', required: true, help: '系统总内存'},
                {key: 'available_memory', label: '可用内存(GB)', type: 'number', required: true, help: '当前可用内存'},
                {key: 'used_memory', label: '已用内存(GB)', type: 'number', required: true, help: '当前已使用内存'},
                {key: 'memory_percent', label: '内存使用率(%)', type: 'number', required: true, help: '内存使用百分比'},
                {key: 'cpu_percent', label: 'CPU使用率(%)', type: 'number', required: false, help: 'CPU使用百分比'},
                {key: 'active_sessions', label: '活跃会话数', type: 'number', required: false, help: '当前活跃的会话数量'},
                {key: 'queue_length', label: '队列长度', type: 'number', required: false, help: '当前任务队列长度'}
            ],
            'test_plan_records': [
                {key: 'original_instruction', label: '原始指令', type: 'textarea', required: true, help: '自然语言测试指令'},
                {key: 'target_platform', label: '目标平台', type: 'select', required: false,
                 options: [
                     {value: '', text: '自动选择'},
                     {value: 'ios', text: 'iOS'},
                     {value: 'android', text: 'Android'}
                 ]},
                {key: 'detected_platform', label: '检测到的平台', type: 'select', required: true,
                 options: [
                     {value: 'ios', text: 'iOS'},
                     {value: 'android', text: 'Android'}
                 ]},
                {key: 'model_name', label: '模型名称', type: 'text', required: true, help: '使用的模型名称'},
                {key: 'status', label: '状态', type: 'select', required: true,
                 options: [
                     {value: 'success', text: '成功'},
                     {value: 'failed', text: '失败'},
                     {value: 'error', text: '错误'}
                 ]},
                {key: 'plan_summary', label: '计划摘要', type: 'textarea', required: false, help: '测试计划的简要描述'},
                {key: 'total_steps', label: '总步骤数', type: 'number', required: false, help: '测试计划包含的步骤数量'}
            ]
        };

        return configs[this.currentTable] || [];
    }
    
    populateForm(data, viewOnly = false) {
        const formConfig = this.getFormConfig();

        formConfig.forEach(field => {
            const element = document.getElementById(field.key);
            if (element && data[field.key] !== undefined && data[field.key] !== null) {
                if (field.type === 'datetime-local') {
                    // 转换ISO日期格式为datetime-local格式
                    const date = new Date(data[field.key]);
                    element.value = date.toISOString().slice(0, 16);
                } else {
                    element.value = data[field.key];
                }
            }
        });

        // 如果是查看模式，添加额外的详细信息显示
        if (viewOnly && this.currentTable === 'test_plan_records') {
            this.addTestPlanDetails(data);
        }
    }

    addTestPlanDetails(data) {
        const container = document.getElementById('form-fields');

        // 添加额外的详细信息字段
        const additionalFields = [];

        if (data.generated_plan) {
            additionalFields.push({
                label: '生成的测试计划',
                value: data.generated_plan,
                type: 'json'
            });
        }

        if (data.execution_result) {
            additionalFields.push({
                label: '执行结果',
                value: data.execution_result,
                type: 'json'
            });
        }

        if (data.error_message) {
            additionalFields.push({
                label: '错误信息',
                value: data.error_message,
                type: 'text'
            });
        }

        if (additionalFields.length > 0) {
            const additionalHtml = additionalFields.map(field => {
                let displayValue = field.value;
                if (field.type === 'json' && typeof field.value === 'object') {
                    displayValue = JSON.stringify(field.value, null, 2);
                }

                return `
                    <div class="form-group">
                        <label>${field.label}</label>
                        <textarea readonly disabled style="min-height: 150px; font-family: monospace;">${displayValue}</textarea>
                    </div>
                `;
            }).join('');

            container.innerHTML += additionalHtml;
        }
    }

    async saveRecord() {
        try {
            const formData = this.getFormData();
            const endpoint = this.getTableEndpoint();

            let response;
            if (this.isEditing) {
                // 更新记录
                response = await fetch(`${endpoint}/${this.editingId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
            } else {
                // 创建新记录
                response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
            }

            const result = await response.json();

            if (response.ok) {
                this.showSuccess(this.isEditing ? '记录更新成功' : '记录创建成功');
                this.hideRecordModal();
                this.loadTableData();
                this.loadOverview();
            } else {
                this.showError(result.detail || '保存失败');
            }

        } catch (error) {
            this.showError('保存记录失败: ' + error.message);
        }
    }

    getFormData() {
        const formConfig = this.getFormConfig();
        const data = {};

        formConfig.forEach(field => {
            const element = document.getElementById(field.key);
            if (element) {
                let value = element.value;

                // 类型转换
                if (field.type === 'number' && value !== '') {
                    value = parseFloat(value);
                } else if (value === '') {
                    value = null;
                }

                // 特殊处理：任务的request_data字段
                if (this.currentTable === 'tasks' && field.key === 'model_name') {
                    data.request_data = {model: value, prompt: 'Test prompt'};
                }

                data[field.key] = value;
            }
        });

        return data;
    }

    async deleteRecord() {
        try {
            const endpoint = this.getTableEndpoint();
            const response = await fetch(`${endpoint}/${this.editingId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (response.ok) {
                this.showSuccess('记录删除成功');
                this.hideConfirmModal();
                this.hideRecordModal();
                this.loadTableData();
                this.loadOverview();
            } else {
                this.showError(result.detail || '删除失败');
            }

        } catch (error) {
            this.showError('删除记录失败: ' + error.message);
        }
    }
}

// 初始化数据库管理器
const dbManager = new DatabaseManager();
