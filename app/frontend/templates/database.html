<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理看板</title>
    <link rel="stylesheet" href="/ui/static/css/style.css">
    <link rel="stylesheet" href="/ui/static/css/database.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🗄️ 数据库管理看板</h1>
            <p class="subtitle">查看和管理本地数据库中的所有数据表</p>
            <div class="header-actions">
                <button class="btn btn-secondary" id="refresh-overview">🔄 刷新概览</button>
                <a href="/ui" class="btn btn-secondary">← 返回主页</a>
            </div>
        </header>

        <!-- 数据库概览 -->
        <div class="overview-section">
            <h2>📊 数据库概览</h2>
            <div class="overview-cards" id="overview-cards">
                <div class="loading">
                    <span>📊 正在加载数据库概览...</span>
                </div>
            </div>
        </div>

        <!-- 表选择和操作 -->
        <nav class="nav-tabs">
            <button class="tab-btn active" data-table="ollama_sessions">🔗 会话管理</button>
            <button class="tab-btn" data-table="tasks">📋 任务队列</button>
            <button class="tab-btn" data-table="memory_stats">📈 内存统计</button>
            <button class="tab-btn" data-table="test_plan_records">🧪 测试计划</button>
        </nav>

        <!-- 数据表内容区域 -->
        <div class="table-content">
            <!-- 操作工具栏 -->
            <div class="table-toolbar">
                <div class="toolbar-left">
                    <h3 id="table-title">会话管理</h3>
                    <span class="record-count" id="record-count">总计: 0 条记录</span>
                </div>
                <div class="toolbar-right">
                    <input type="text" id="search-input" placeholder="搜索..." class="search-input">
                    <select id="status-filter" class="filter-select">
                        <option value="">所有状态</option>
                    </select>
                    <button class="btn btn-primary" id="add-record-btn">➕ 新增记录</button>
                    <button class="btn btn-secondary" id="refresh-table-btn">🔄 刷新</button>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="table-container">
                <div class="table-loading" id="table-loading">
                    <span>📊 正在加载数据...</span>
                </div>
                <table class="data-table" id="data-table" style="display: none;">
                    <thead id="table-head">
                        <!-- 动态生成表头 -->
                    </thead>
                    <tbody id="table-body">
                        <!-- 动态生成表格内容 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页控件 -->
            <div class="pagination" id="pagination" style="display: none;">
                <button class="btn btn-secondary" id="prev-page">← 上一页</button>
                <span class="page-info" id="page-info">第 1 页，共 1 页</span>
                <button class="btn btn-secondary" id="next-page">下一页 →</button>
                <select id="page-size-select" class="page-size-select">
                    <option value="10">10条/页</option>
                    <option value="20" selected>20条/页</option>
                    <option value="50">50条/页</option>
                    <option value="100">100条/页</option>
                </select>
            </div>
        </div>

        <!-- 记录详情模态框 -->
        <div class="modal" id="record-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modal-title">记录详情</h3>
                    <button class="modal-close" id="modal-close">×</button>
                </div>
                <div class="modal-body">
                    <form id="record-form">
                        <div id="form-fields">
                            <!-- 动态生成表单字段 -->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" id="save-record">💾 保存</button>
                    <button class="btn btn-danger" id="delete-record" style="display: none;">🗑️ 删除</button>
                    <button class="btn btn-secondary" id="cancel-modal">取消</button>
                </div>
            </div>
        </div>

        <!-- 确认删除模态框 -->
        <div class="modal" id="confirm-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>⚠️ 确认删除</h3>
                    <button class="modal-close" id="confirm-modal-close">×</button>
                </div>
                <div class="modal-body">
                    <p id="confirm-message">确定要删除这条记录吗？此操作不可撤销。</p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-danger" id="confirm-delete">🗑️ 确认删除</button>
                    <button class="btn btn-secondary" id="cancel-delete">取消</button>
                </div>
            </div>
        </div>

        <!-- 错误提示 -->
        <div class="error-toast" id="error-toast" style="display: none;">
            <span id="error-message"></span>
            <button class="toast-close" id="error-close">×</button>
        </div>

        <!-- 成功提示 -->
        <div class="success-toast" id="success-toast" style="display: none;">
            <span id="success-message"></span>
            <button class="toast-close" id="success-close">×</button>
        </div>
    </div>

    <script src="/ui/static/js/database.js"></script>
</body>
</html>
