import logging
import logging.handlers
from pathlib import Path
from app.config import config

def setup_logging():
    """配置日志系统"""
    
    # 确保日志目录存在
    Path("logs").mkdir(exist_ok=True)
    
    # 日志格式
    formatter = logging.Formatter(
        config.get('logging.format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    )
    
    # 应用日志
    app_handler = logging.handlers.RotatingFileHandler(
        config.get('logging.files.app', 'logs/app.log'),
        maxBytes=config.get('logging.max_file_size', 10) * 1024 * 1024,  # MB转字节
        backupCount=config.get('logging.backup_count', 5),
        encoding='utf-8'
    )
    app_handler.setFormatter(formatter)
    app_handler.setLevel(logging.INFO)
    
    # API访问日志
    api_handler = logging.handlers.RotatingFileHandler(
        config.get('logging.files.api', 'logs/api.log'),
        maxBytes=config.get('logging.max_file_size', 10) * 1024 * 1024,
        backupCount=config.get('logging.backup_count', 5),
        encoding='utf-8'
    )
    api_handler.setFormatter(formatter)
    api_handler.setLevel(logging.INFO)
    
    # 错误日志
    error_handler = logging.handlers.RotatingFileHandler(
        config.get('logging.files.error', 'logs/error.log'),
        maxBytes=config.get('logging.max_file_size', 10) * 1024 * 1024,
        backupCount=config.get('logging.backup_count', 5),
        encoding='utf-8'
    )
    error_handler.setFormatter(formatter)
    error_handler.setLevel(logging.ERROR)
    
    # 控制台输出
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.get('logging.level', 'INFO').upper()))
    root_logger.addHandler(app_handler)
    root_logger.addHandler(error_handler)
    root_logger.addHandler(console_handler)
    
    # 配置API日志器
    api_logger = logging.getLogger('api')
    api_logger.addHandler(api_handler)
    api_logger.propagate = False  # 不传播到根日志器
    
    return {
        'app': logging.getLogger('app'),
        'api': logging.getLogger('api'),
        'error': logging.getLogger('error')
    }