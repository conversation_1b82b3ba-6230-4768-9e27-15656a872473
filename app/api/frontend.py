from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.templating import Jinja2Templates
from pathlib import Path
import os
import logging

logger = logging.getLogger(__name__)

# 获取前端文件路径
frontend_dir = Path(__file__).parent.parent / "frontend"
templates = Jinja2Templates(directory=str(frontend_dir / "templates"))
static_dir = frontend_dir / "static"

router = APIRouter(prefix="/ui", tags=["frontend"])

@router.get("/", response_class=HTMLResponse, summary="前端主页")
async def frontend_index(request: Request):
    """
    返回前端主页面
    """
    try:
        return templates.TemplateResponse("index.html", {"request": request})
    except Exception as e:
        logger.error(f"渲染前端页面失败: {e}")
        return HTMLResponse(
            content="<h1>页面加载失败</h1><p>请检查前端模板文件</p>",
            status_code=500
        )

@router.get("/static/css/{file_path:path}")
async def serve_css(file_path: str):
    """
    提供CSS静态文件
    """
    file_location = static_dir / "css" / file_path
    
    if not file_location.exists():
        logger.warning(f"CSS文件不存在: {file_location}")
        return HTMLResponse(content="/* CSS file not found */", status_code=404)
    
    return FileResponse(
        path=file_location,
        media_type="text/css",
        filename=file_path
    )

@router.get("/static/js/{file_path:path}")
async def serve_js(file_path: str):
    """
    提供JavaScript静态文件
    """
    file_location = static_dir / "js" / file_path
    
    if not file_location.exists():
        logger.warning(f"JS文件不存在: {file_location}")
        return HTMLResponse(content="// JS file not found", status_code=404)
    
    return FileResponse(
        path=file_location,
        media_type="application/javascript",
        filename=file_path
    )

@router.get("/static/{file_type}/{file_path:path}")
async def serve_static_files(file_type: str, file_path: str):
    """
    提供其他静态文件（图片、字体等）
    """
    file_location = static_dir / file_type / file_path
    
    if not file_location.exists():
        logger.warning(f"静态文件不存在: {file_location}")
        return HTMLResponse(content="File not found", status_code=404)
    
    # 根据文件扩展名确定MIME类型
    mime_types = {
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.ico': 'image/x-icon',
        '.woff': 'font/woff',
        '.woff2': 'font/woff2',
        '.ttf': 'font/ttf',
        '.eot': 'application/vnd.ms-fontobject'
    }
    
    file_ext = Path(file_path).suffix.lower()
    media_type = mime_types.get(file_ext, 'application/octet-stream')
    
    return FileResponse(
        path=file_location,
        media_type=media_type,
        filename=file_path
    )

@router.get("/health", summary="前端健康检查")
async def frontend_health():
    """
    前端服务健康检查
    """
    try:
        # 检查前端文件是否存在
        template_exists = (frontend_dir / "templates" / "index.html").exists()
        css_exists = (static_dir / "css" / "style.css").exists()
        js_exists = (static_dir / "js" / "app.js").exists()
        
        status = "healthy" if all([template_exists, css_exists, js_exists]) else "unhealthy"
        
        return {
            "status": status,
            "frontend_files": {
                "template": template_exists,
                "css": css_exists,
                "javascript": js_exists
            },
            "static_directory": str(static_dir),
            "template_directory": str(frontend_dir / "templates")
        }
    except Exception as e:
        logger.error(f"前端健康检查失败: {e}")
        return {
            "status": "error",
            "error": str(e)
        }