from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Union
import uuid
import logging

from app.services.ollama_proxy import ollama_proxy

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ollama", tags=["ollama"])

# Pydantic模型定义
class GenerateRequest(BaseModel):
    """Generate请求模型"""
    model: str = Field(..., description="模型名称")
    prompt: str = Field(..., description="提示词")
    stream: Optional[bool] = Field(default=True, description="是否流式输出")
    raw: Optional[bool] = Field(default=False, description="是否原始模式")
    format: Optional[str] = Field(default=None, description="输出格式")
    options: Optional[Dict[str, Any]] = Field(default=None, description="模型参数")
    system: Optional[str] = Field(default=None, description="系统提示")
    template: Optional[str] = Field(default=None, description="模板")
    context: Optional[List[int]] = Field(default=None, description="上下文")
    keep_alive: Optional[Union[int, str]] = Field(default=None, description="保持活跃时间")

class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: str = Field(..., description="角色(user/assistant/system)")
    content: str = Field(..., description="消息内容")
    images: Optional[List[str]] = Field(default=None, description="图像数据")

class ChatRequest(BaseModel):
    """Chat请求模型"""
    model: str = Field(..., description="模型名称")
    messages: List[ChatMessage] = Field(..., description="消息历史")
    stream: Optional[bool] = Field(default=True, description="是否流式输出")
    format: Optional[str] = Field(default=None, description="输出格式")
    options: Optional[Dict[str, Any]] = Field(default=None, description="模型参数")
    keep_alive: Optional[Union[int, str]] = Field(default=None, description="保持活跃时间")

class ModelInfo(BaseModel):
    """模型信息模型"""
    name: str
    model: str
    modified_at: str
    size: int
    digest: str
    details: Dict[str, Any]

class ModelsResponse(BaseModel):
    """模型列表响应"""
    models: List[ModelInfo]

@router.get("/", summary="服务状态检查")
async def health_check():
    """健康检查端点"""
    try:
        is_healthy = await ollama_proxy.check_health()
        if is_healthy:
            return {
                "status": "healthy",
                "service": "overall_api_server",
                "ollama_backend": ollama_proxy.base_url,
                "version": "1.0.0"
            }
        else:
            raise HTTPException(status_code=503, detail="Ollama服务不可用")
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="服务检查失败")

@router.get("/models", response_model=ModelsResponse, summary="获取可用模型列表")
async def get_models():
    """获取ollama中可用的模型列表"""
    try:
        models_data = await ollama_proxy.get_models()
        return models_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取模型列表失败")

@router.post("/generate", summary="文本生成")
async def generate(request: GenerateRequest):
    """
    Generate端点代理
    支持流式和非流式输出，完全兼容ollama API
    """
    try:
        # 生成任务ID用于追踪
        task_id = str(uuid.uuid4())
        
        # 验证模型是否存在
        if not await ollama_proxy.validate_model(request.model):
            raise HTTPException(status_code=400, detail=f"模型 {request.model} 不存在")
        
        # 转换为字典格式
        request_data = request.dict(exclude_unset=True)
        
        logger.info(f"接收generate请求 - 模型: {request.model}, 流式: {request.stream}, 任务ID: {task_id}")
        
        if request.stream:
            # 流式响应
            return StreamingResponse(
                ollama_proxy.generate_stream(request_data, task_id),
                media_type="application/x-ndjson",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Task-ID": task_id
                }
            )
        else:
            # 非流式响应
            result = await ollama_proxy.generate_sync(request_data, task_id)
            return JSONResponse(
                content=result,
                headers={"X-Task-ID": task_id}
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Generate请求处理失败: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")

@router.post("/chat", summary="对话聊天")
async def chat(request: ChatRequest):
    """
    Chat端点代理
    支持流式和非流式输出，完全兼容ollama API
    """
    try:
        # 生成任务ID用于追踪
        task_id = str(uuid.uuid4())
        
        # 验证模型是否存在
        if not await ollama_proxy.validate_model(request.model):
            raise HTTPException(status_code=400, detail=f"模型 {request.model} 不存在")
        
        # 转换为字典格式
        request_data = request.dict(exclude_unset=True)
        
        # 转换messages为字典格式
        if request_data.get('messages'):
            request_data['messages'] = [
                msg.dict(exclude_unset=True) for msg in request.messages
            ]
        
        logger.info(f"接收chat请求 - 模型: {request.model}, 流式: {request.stream}, 任务ID: {task_id}")
        
        if request.stream:
            # 流式响应
            return StreamingResponse(
                ollama_proxy.chat_stream(request_data, task_id),
                media_type="application/x-ndjson",
                headers={
                    "Cache-Control": "no-cache", 
                    "Connection": "keep-alive",
                    "X-Task-ID": task_id
                }
            )
        else:
            # 非流式响应
            result = await ollama_proxy.chat_sync(request_data, task_id)
            return JSONResponse(
                content=result,
                headers={"X-Task-ID": task_id}
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chat请求处理失败: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")

@router.get("/tags", summary="获取模型标签")
async def get_tags():
    """获取模型标签，兼容ollama原生API"""
    return await get_models()

# 兼容性端点（支持不带/ollama前缀的请求）
@router.post("/api/generate", summary="Generate (兼容性)")
async def generate_compat(request: GenerateRequest):
    """兼容ollama原生API路径"""
    return await generate(request)

@router.post("/api/chat", summary="Chat (兼容性)")  
async def chat_compat(request: ChatRequest):
    """兼容ollama原生API路径"""
    return await chat(request)

@router.get("/api/tags", summary="Tags (兼容性)")
async def tags_compat():
    """兼容ollama原生API路径"""
    return await get_models()