from fastapi import APIRouter, HTTPException, Request, Query, Depends
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc, or_
import uuid
import json
import logging
from datetime import datetime

from app.services.test_plan_generator import test_plan_generator
from app.models.database import get_db_session
from app.models.task import TestPlanRecord, TestPlanStatus

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/test-plan", tags=["test-plan"])

# Pydantic模型定义
class TestPlanRequest(BaseModel):
    """测试计划生成请求模型"""
    instruction: str = Field(..., min_length=1, max_length=2000, description="自然语言测试指令")
    platform: Optional[str] = Field(default=None, description="目标平台：ios或android（不指定时自动轮询）")
    model: Optional[str] = Field(default=None, description="使用的模型（不指定时使用默认模型）")
    stream: bool = Field(default=False, description="是否流式响应")

class TestPlanResponse(BaseModel):
    """测试计划响应模型"""
    status: str
    record_id: str
    plan: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_time: float
    detected_platform: Optional[str] = None
    model_name: Optional[str] = None

class TestPlanRecordSummary(BaseModel):
    """测试计划记录摘要模型"""
    record_id: str
    original_instruction: str
    detected_platform: str
    status: str
    plan_id: Optional[str]
    plan_summary: Optional[str]
    total_steps: Optional[int]
    processing_time: Optional[float]
    created_at: str

class TestPlanRecordDetail(BaseModel):
    """测试计划记录详情模型"""
    record_id: str
    original_instruction: str
    target_platform: Optional[str]
    detected_platform: str
    model_name: str
    status: str
    generated_plan: Optional[Dict[str, Any]]
    plan_id: Optional[str]
    plan_summary: Optional[str]
    total_steps: Optional[int]
    error_message: Optional[str]
    processing_time: Optional[float]
    response_length: Optional[int]
    client_ip: Optional[str]
    created_at: str
    updated_at: str

class TestPlanRecordsResponse(BaseModel):
    """测试计划记录列表响应模型"""
    records: List[TestPlanRecordSummary]
    total: int
    page: int
    page_size: int
    has_next: bool

class TestPlanStats(BaseModel):
    """测试计划统计模型"""
    total_records: int
    success_records: int
    failed_records: int
    error_records: int
    success_rate: float
    platform_distribution: Dict[str, int]
    recent_activity: List[Dict[str, Any]]

# 依赖函数
async def get_db() -> AsyncSession:
    """获取数据库会话"""
    async for session in get_db_session():
        yield session

def get_client_info(request: Request) -> Dict[str, Optional[str]]:
    """获取客户端信息"""
    return {
        "client_ip": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent")
    }

async def test_plan_stream_wrapper(stream_generator, record_id: str, client_info: Dict, 
                                 db: AsyncSession, request: TestPlanRequest):
    """测试计划流式响应包装器，处理数据库记录"""
    final_result = None
    
    try:
        async for chunk in stream_generator:
            yield chunk
            
            # 检查是否是最终结果
            if isinstance(chunk, str) and chunk.startswith("data: "):
                try:
                    json_str = chunk[6:].strip()  # 去掉 "data: " 前缀
                    if json_str:
                        chunk_data = json.loads(json_str)
                        if chunk_data.get("type") == "final":
                            final_result = chunk_data
                except Exception as e:
                    logger.debug(f"解析最终结果时出错: {e}")
                    pass
    
    finally:
        # 保存最终记录到数据库
        if final_result:
            try:
                await save_test_plan_record(db, record_id, request, client_info, final_result)
            except Exception as e:
                logger.error(f"保存流式测试计划记录失败: {e}")

async def save_test_plan_record(db: AsyncSession, record_id: str, request: TestPlanRequest, 
                              client_info: Dict, result: Dict):
    """保存测试计划记录到数据库"""
    try:
        record_data = {
            "record_id": record_id,
            "original_instruction": request.instruction,
            "target_platform": request.platform,
            "detected_platform": result.get("detected_platform", "unknown"),
            "model_name": result.get("model_name", request.model or "deepseek-r1:32b"),
            "processing_time": result.get("processing_time"),
            "response_length": result.get("response_length"),
            "client_ip": client_info.get("client_ip"),
            "user_agent": client_info.get("user_agent")
        }
        
        if result["status"] == "success":
            plan = result["plan"]
            record_data.update({
                "status": TestPlanStatus.SUCCESS,
                "generated_plan": plan,
                "plan_id": plan.get("plan_id"),
                "plan_summary": plan.get("summary"),
                "total_steps": plan.get("total_steps"),
                "prompt_tokens": result.get("prompt_tokens")
            })
        else:
            record_data.update({
                "status": TestPlanStatus.FAILED if result.get("error", "").startswith("模型") else TestPlanStatus.ERROR,
                "error_message": result.get("error"),
                "raw_response": result.get("raw_response", "")
            })
        
        test_plan_record = TestPlanRecord(**record_data)
        db.add(test_plan_record)
        await db.commit()
        
        logger.info(f"流式测试计划记录已保存 - 记录ID: {record_id}")
        
    except Exception as e:
        logger.error(f"保存测试计划记录失败: {e}")
        await db.rollback()

@router.post("/generate", response_model=TestPlanResponse, summary="生成测试计划")
async def generate_test_plan(
    request: TestPlanRequest,
    http_request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    根据自然语言指令生成结构化测试计划
    
    - **instruction**: 自然语言测试指令，例如"在iOS设备上打开美团首页，点击搜索框，输入'火锅'，然后截图"
    - **platform**: 目标平台（可选），不指定时自动在iOS和Android之间轮询
    - **model**: 使用的模型（可选），不指定时使用默认的DeepSeek-r1:32b
    - **stream**: 是否流式响应
    """
    record_id = str(uuid.uuid4())
    client_info = get_client_info(http_request)
    
    try:
        logger.info(f"开始生成测试计划 - 记录ID: {record_id}")
        
        # 调用测试计划生成器
        result = await test_plan_generator.generate_plan(
            natural_language_request=request.instruction,
            platform=request.platform,
            model=request.model,
            stream=request.stream
        )
        
        # 如果是流式响应
        if request.stream and result.get("stream"):
            return StreamingResponse(
                test_plan_stream_wrapper(
                    result["stream_generator"],
                    record_id,
                    client_info,
                    db,
                    request
                ),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Record-ID": record_id
                }
            )
        
        # 准备记录数据
        record_data = {
            "record_id": record_id,
            "original_instruction": request.instruction,
            "target_platform": request.platform,
            "detected_platform": result.get("detected_platform", "unknown"),
            "model_name": result.get("model_name", request.model or test_plan_generator.default_model),
            "processing_time": result.get("processing_time"),
            "response_length": result.get("response_length"),
            "client_ip": client_info.get("client_ip"),
            "user_agent": client_info.get("user_agent")
        }
        
        if result["status"] == "success":
            # 成功情况
            plan = result["plan"]
            record_data.update({
                "status": TestPlanStatus.SUCCESS,
                "generated_plan": plan,
                "plan_id": plan.get("plan_id"),
                "plan_summary": plan.get("summary"),
                "total_steps": plan.get("total_steps"),
                "prompt_tokens": result.get("prompt_tokens")
            })
            
            # 保存成功记录
            test_plan_record = TestPlanRecord(**record_data)
            db.add(test_plan_record)
            await db.commit()
            
            logger.info(f"测试计划生成成功 - 记录ID: {record_id}, 计划ID: {plan.get('plan_id')}")
            
            return TestPlanResponse(
                status="success",
                record_id=record_id,
                plan=plan,
                processing_time=result["processing_time"],
                detected_platform=result["detected_platform"],
                model_name=result["model_name"]
            )
        else:
            # 失败情况
            record_data.update({
                "status": TestPlanStatus.FAILED if result.get("error", "").startswith("模型") else TestPlanStatus.ERROR,
                "error_message": result.get("error"),
                "raw_response": result.get("raw_response", "")
            })
            
            # 保存失败记录
            test_plan_record = TestPlanRecord(**record_data)
            db.add(test_plan_record)
            await db.commit()
            
            logger.error(f"测试计划生成失败 - 记录ID: {record_id}, 错误: {result.get('error')}")
            
            return TestPlanResponse(
                status="error",
                record_id=record_id,
                error=result.get("error"),
                processing_time=result["processing_time"],
                detected_platform=result.get("detected_platform"),
                model_name=result.get("model_name")
            )
            
    except Exception as e:
        logger.error(f"生成测试计划时发生异常 - 记录ID: {record_id}: {str(e)}")
        
        try:
            # 保存异常记录
            record_data.update({
                "status": TestPlanStatus.ERROR,
                "error_message": f"服务器内部错误: {str(e)}",
                "processing_time": 0.0
            })
            
            test_plan_record = TestPlanRecord(**record_data)
            db.add(test_plan_record)
            await db.commit()
        except Exception as db_error:
            logger.error(f"保存错误记录失败: {str(db_error)}")
        
        raise HTTPException(status_code=500, detail="服务器内部错误")

@router.get("/records", response_model=TestPlanRecordsResponse, summary="获取测试计划记录列表")
async def get_test_plan_records(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词（在指令和摘要中搜索）"),
    platform: Optional[str] = Query(None, description="平台筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取测试计划记录列表，支持分页和搜索
    
    - **page**: 页码，从1开始
    - **page_size**: 每页数量，最大100
    - **search**: 搜索关键词，在原始指令和计划摘要中搜索
    - **platform**: 平台筛选（ios/android）
    - **status**: 状态筛选（success/failed/error）
    """
    try:
        # 构建查询
        query = select(TestPlanRecord)
        count_query = select(func.count(TestPlanRecord.id))
        
        # 搜索筛选
        if search:
            search_filter = or_(
                TestPlanRecord.original_instruction.contains(search),
                TestPlanRecord.plan_summary.contains(search),
                TestPlanRecord.plan_id.contains(search)
            )
            query = query.where(search_filter)
            count_query = count_query.where(search_filter)
        
        # 平台筛选
        if platform:
            platform_filter = TestPlanRecord.detected_platform == platform.lower()
            query = query.where(platform_filter)
            count_query = count_query.where(platform_filter)
        
        # 状态筛选
        if status:
            try:
                status_enum = TestPlanStatus(status.lower())
                status_filter = TestPlanRecord.status == status_enum
                query = query.where(status_filter)
                count_query = count_query.where(status_filter)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"无效的状态值: {status}")
        
        # 排序和分页
        query = query.order_by(desc(TestPlanRecord.created_at))
        query = query.offset((page - 1) * page_size).limit(page_size)
        
        # 执行查询
        records_result = await db.execute(query)
        records = records_result.scalars().all()
        
        count_result = await db.execute(count_query)
        total = count_result.scalar() or 0
        
        # 转换为摘要格式
        record_summaries = [
            TestPlanRecordSummary(**record.to_summary_dict())
            for record in records
        ]
        
        has_next = (page * page_size) < total
        
        return TestPlanRecordsResponse(
            records=record_summaries,
            total=total,
            page=page,
            page_size=page_size,
            has_next=has_next
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取测试计划记录列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取记录列表失败")

@router.get("/records/{record_id}", response_model=TestPlanRecordDetail, summary="获取测试计划记录详情")
async def get_test_plan_record(
    record_id: str,
    db: AsyncSession = Depends(get_db)
):
    """
    获取指定测试计划记录的详细信息
    
    - **record_id**: 记录ID
    """
    try:
        query = select(TestPlanRecord).where(TestPlanRecord.record_id == record_id)
        result = await db.execute(query)
        record = result.scalar_one_or_none()
        
        if not record:
            raise HTTPException(status_code=404, detail="记录不存在")
        
        return TestPlanRecordDetail(**record.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取测试计划记录详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取记录详情失败")

@router.get("/stats", response_model=TestPlanStats, summary="获取测试计划统计信息")
async def get_test_plan_stats(
    db: AsyncSession = Depends(get_db)
):
    """
    获取测试计划的统计信息
    
    包括总记录数、成功率、平台分布、最近活动等
    """
    try:
        # 总记录数
        total_query = select(func.count(TestPlanRecord.id))
        total_result = await db.execute(total_query)
        total_records = total_result.scalar() or 0
        
        if total_records == 0:
            return TestPlanStats(
                total_records=0,
                success_records=0,
                failed_records=0,
                error_records=0,
                success_rate=0.0,
                platform_distribution={},
                recent_activity=[]
            )
        
        # 状态统计
        status_query = select(
            TestPlanRecord.status,
            func.count(TestPlanRecord.id)
        ).group_by(TestPlanRecord.status)
        status_result = await db.execute(status_query)
        status_counts = dict(status_result.fetchall())
        
        success_records = status_counts.get(TestPlanStatus.SUCCESS, 0)
        failed_records = status_counts.get(TestPlanStatus.FAILED, 0)
        error_records = status_counts.get(TestPlanStatus.ERROR, 0)
        success_rate = (success_records / total_records * 100) if total_records > 0 else 0.0
        
        # 平台分布
        platform_query = select(
            TestPlanRecord.detected_platform,
            func.count(TestPlanRecord.id)
        ).group_by(TestPlanRecord.detected_platform)
        platform_result = await db.execute(platform_query)
        platform_distribution = dict(platform_result.fetchall())
        
        # 最近活动（最近10条记录）
        recent_query = select(TestPlanRecord).order_by(
            desc(TestPlanRecord.created_at)
        ).limit(10)
        recent_result = await db.execute(recent_query)
        recent_records = recent_result.scalars().all()
        
        recent_activity = [
            {
                "record_id": record.record_id,
                "instruction": record.original_instruction[:50] + "..." if len(record.original_instruction) > 50 else record.original_instruction,
                "platform": record.detected_platform,
                "status": record.status.value,
                "created_at": record.created_at.isoformat() if record.created_at else None
            }
            for record in recent_records
        ]
        
        return TestPlanStats(
            total_records=total_records,
            success_records=success_records,
            failed_records=failed_records,
            error_records=error_records,
            success_rate=round(success_rate, 2),
            platform_distribution=platform_distribution,
            recent_activity=recent_activity
        )
        
    except Exception as e:
        logger.error(f"获取测试计划统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取统计信息失败")

@router.get("/platform-info", summary="获取平台信息")
async def get_platform_info():
    """获取当前平台轮询状态"""
    try:
        stats = test_plan_generator.get_platform_stats()
        return JSONResponse(content=stats)
    except Exception as e:
        logger.error(f"获取平台信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取平台信息失败")

@router.get("/prompt-info", summary="获取Prompt说明信息")
async def get_prompt_info():
    """获取系统使用的prompt模板信息"""
    try:
        # 获取prompt信息
        prompt_info = test_plan_generator.get_prompt_info()
        return JSONResponse(content=prompt_info)
    except Exception as e:
        logger.error(f"获取prompt信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取prompt信息失败")

@router.get("/model-info", summary="获取当前模型信息")
async def get_model_info():
    """获取当前使用的模型信息"""
    try:
        # 获取模型信息 (注意这是async函数)
        model_info = await test_plan_generator.get_model_info()
        return JSONResponse(content=model_info)
    except Exception as e:
        logger.error(f"获取模型信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取模型信息失败")