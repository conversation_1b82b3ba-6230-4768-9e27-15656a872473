from fastapi import APIRouter, HTTPException, Query, Depends, Body
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc, asc, or_, and_, delete, update
from sqlalchemy.orm import selectinload
import uuid
import json
import logging
from datetime import datetime

from app.models.database import get_db_session
from app.models.session import OllamaSession, SessionStatus
from app.models.task import Task, TaskStatus, TaskType, MemoryStat, TestPlanRecord, TestPlanStatus

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/database", tags=["database"])

# 依赖注入
async def get_db():
    async for session in get_db_session():
        yield session

# ============= 通用响应模型 =============
class PaginatedResponse(BaseModel):
    """分页响应基类"""
    total: int
    page: int
    page_size: int
    total_pages: int
    data: List[Dict[str, Any]]

class DatabaseStats(BaseModel):
    """数据库统计信息"""
    table_name: str
    total_records: int
    recent_records: int  # 最近24小时
    
class DatabaseOverview(BaseModel):
    """数据库概览"""
    tables: List[DatabaseStats]
    total_records: int
    database_size: Optional[str] = None

# ============= 请求模型 =============
class CreateSessionRequest(BaseModel):
    """创建会话请求"""
    model_config = {"protected_namespaces": ()}

    model_name: str = Field(..., description="模型名称")
    process_id: Optional[int] = Field(None, description="进程ID")
    port: Optional[int] = Field(None, description="端口")
    memory_usage: Optional[float] = Field(None, description="内存使用量(GB)")
    model_size: Optional[float] = Field(None, description="模型大小(GB)")
    max_context_length: Optional[int] = Field(None, description="最大上下文长度")
    temperature: Optional[float] = Field(None, description="温度参数")
    session_config: Optional[Dict[str, Any]] = Field(None, description="会话配置")

class UpdateSessionRequest(BaseModel):
    """更新会话请求"""
    model_config = {"protected_namespaces": ()}

    model_name: Optional[str] = Field(None, description="模型名称")
    status: Optional[SessionStatus] = Field(None, description="会话状态")
    process_id: Optional[int] = Field(None, description="进程ID")
    port: Optional[int] = Field(None, description="端口")
    memory_usage: Optional[float] = Field(None, description="内存使用量(GB)")
    model_size: Optional[float] = Field(None, description="模型大小(GB)")
    max_context_length: Optional[int] = Field(None, description="最大上下文长度")
    temperature: Optional[float] = Field(None, description="温度参数")
    session_config: Optional[Dict[str, Any]] = Field(None, description="会话配置")
    is_healthy: Optional[bool] = Field(None, description="健康状态")
    last_error: Optional[str] = Field(None, description="最后错误信息")

class CreateTaskRequest(BaseModel):
    """创建任务请求"""
    model_config = {"protected_namespaces": ()}

    task_type: TaskType = Field(..., description="任务类型")
    model_name: str = Field(..., description="模型名称")
    request_data: Dict[str, Any] = Field(..., description="请求数据")
    session_id: Optional[str] = Field(None, description="会话ID")

class UpdateTaskRequest(BaseModel):
    """更新任务请求"""
    status: Optional[TaskStatus] = Field(None, description="任务状态")
    session_id: Optional[str] = Field(None, description="会话ID")
    queue_position: Optional[int] = Field(None, description="队列位置")
    estimated_wait_time: Optional[float] = Field(None, description="预估等待时间")
    response_data: Optional[str] = Field(None, description="响应数据")
    error_message: Optional[str] = Field(None, description="错误信息")
    queue_time: Optional[float] = Field(None, description="排队时间")
    process_time: Optional[float] = Field(None, description="处理时间")
    total_tokens: Optional[int] = Field(None, description="token数量")

class CreateMemoryStatRequest(BaseModel):
    """创建内存统计请求"""
    total_memory: float = Field(..., description="总内存(GB)")
    available_memory: float = Field(..., description="可用内存(GB)")
    used_memory: float = Field(..., description="已用内存(GB)")
    memory_percent: float = Field(..., description="内存使用百分比")
    cpu_percent: Optional[float] = Field(None, description="CPU使用率")
    active_sessions: Optional[int] = Field(0, description="活跃会话数")
    queue_length: Optional[int] = Field(0, description="队列长度")

class CreateTestPlanRequest(BaseModel):
    """创建测试计划记录请求"""
    model_config = {"protected_namespaces": ()}

    original_instruction: str = Field(..., description="原始指令")
    target_platform: Optional[str] = Field(None, description="目标平台")
    detected_platform: str = Field(..., description="检测到的平台")
    model_name: str = Field(..., description="模型名称")
    status: TestPlanStatus = Field(..., description="状态")
    generated_plan: Optional[Dict[str, Any]] = Field(None, description="生成的计划")
    plan_id: Optional[str] = Field(None, description="计划ID")
    plan_summary: Optional[str] = Field(None, description="计划摘要")
    total_steps: Optional[int] = Field(None, description="总步骤数")
    error_message: Optional[str] = Field(None, description="错误信息")
    raw_response: Optional[str] = Field(None, description="原始响应")
    processing_time: Optional[float] = Field(None, description="处理时间")
    prompt_tokens: Optional[int] = Field(None, description="prompt tokens")
    response_length: Optional[int] = Field(None, description="响应长度")
    client_ip: Optional[str] = Field(None, description="客户端IP")
    user_agent: Optional[str] = Field(None, description="用户代理")

# ============= 数据库概览端点 =============
@router.get("/overview", response_model=DatabaseOverview, summary="获取数据库概览")
async def get_database_overview(db: AsyncSession = Depends(get_db)):
    """获取数据库概览信息，包括各表的记录统计"""
    try:
        tables_stats = []
        total_records = 0
        
        # 获取各表统计
        tables = [
            ("ollama_sessions", OllamaSession),
            ("tasks", Task),
            ("memory_stats", MemoryStat),
            ("test_plan_records", TestPlanRecord)
        ]
        
        for table_name, model_class in tables:
            # 总记录数
            total_query = select(func.count(model_class.id))
            total_result = await db.execute(total_query)
            table_total = total_result.scalar() or 0

            # 最近24小时记录数 (SQLite兼容)
            from datetime import datetime, timedelta
            yesterday = datetime.now() - timedelta(days=1)

            # 根据不同模型使用不同的时间字段
            if model_class == MemoryStat:
                time_field = model_class.timestamp
            else:
                time_field = model_class.created_at

            recent_query = select(func.count(model_class.id)).where(
                time_field >= yesterday
            )
            recent_result = await db.execute(recent_query)
            table_recent = recent_result.scalar() or 0
            
            tables_stats.append(DatabaseStats(
                table_name=table_name,
                total_records=table_total,
                recent_records=table_recent
            ))
            
            total_records += table_total
        
        return DatabaseOverview(
            tables=tables_stats,
            total_records=total_records
        )
        
    except Exception as e:
        logger.error(f"获取数据库概览失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取数据库概览失败")

# ============= 会话管理端点 =============
@router.get("/sessions", response_model=PaginatedResponse, summary="获取会话列表")
async def get_sessions(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    status: Optional[SessionStatus] = Query(None, description="状态筛选"),
    model_name: Optional[str] = Query(None, description="模型筛选"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向"),
    db: AsyncSession = Depends(get_db)
):
    """获取会话列表，支持分页、搜索和筛选"""
    try:
        # 构建查询
        query = select(OllamaSession)
        count_query = select(func.count(OllamaSession.id))

        # 搜索筛选
        if search:
            search_filter = or_(
                OllamaSession.session_id.contains(search),
                OllamaSession.model_name.contains(search),
                OllamaSession.last_error.contains(search)
            )
            query = query.where(search_filter)
            count_query = count_query.where(search_filter)

        # 状态筛选
        if status:
            status_filter = OllamaSession.status == status
            query = query.where(status_filter)
            count_query = count_query.where(status_filter)

        # 模型筛选
        if model_name:
            model_filter = OllamaSession.model_name == model_name
            query = query.where(model_filter)
            count_query = count_query.where(model_filter)

        # 排序
        if hasattr(OllamaSession, sort_by):
            sort_column = getattr(OllamaSession, sort_by)
            if sort_order.lower() == "asc":
                query = query.order_by(asc(sort_column))
            else:
                query = query.order_by(desc(sort_column))

        # 分页
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)

        # 执行查询
        total_result = await db.execute(count_query)
        total = total_result.scalar() or 0

        result = await db.execute(query)
        sessions = result.scalars().all()

        # 转换为字典
        data = [session.to_dict() for session in sessions]

        total_pages = (total + page_size - 1) // page_size

        return PaginatedResponse(
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            data=data
        )

    except Exception as e:
        logger.error(f"获取会话列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取会话列表失败")

@router.get("/sessions/{session_id}", summary="获取会话详情")
async def get_session(session_id: str, db: AsyncSession = Depends(get_db)):
    """获取指定会话的详细信息"""
    try:
        query = select(OllamaSession).where(OllamaSession.session_id == session_id)
        result = await db.execute(query)
        session = result.scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")

        return session.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取会话详情失败")

@router.post("/sessions", summary="创建会话")
async def create_session(
    request: CreateSessionRequest,
    db: AsyncSession = Depends(get_db)
):
    """创建新的会话记录"""
    try:
        session_id = str(uuid.uuid4())

        session = OllamaSession(
            session_id=session_id,
            model_name=request.model_name,
            process_id=request.process_id,
            port=request.port,
            memory_usage=request.memory_usage,
            model_size=request.model_size,
            max_context_length=request.max_context_length,
            temperature=request.temperature,
            session_config=request.session_config
        )

        db.add(session)
        await db.commit()
        await db.refresh(session)

        return {
            "message": "会话创建成功",
            "session_id": session_id,
            "data": session.to_dict()
        }

    except Exception as e:
        await db.rollback()
        logger.error(f"创建会话失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建会话失败")

@router.put("/sessions/{session_id}", summary="更新会话")
async def update_session(
    session_id: str,
    request: UpdateSessionRequest,
    db: AsyncSession = Depends(get_db)
):
    """更新指定会话的信息"""
    try:
        query = select(OllamaSession).where(OllamaSession.session_id == session_id)
        result = await db.execute(query)
        session = result.scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")

        # 更新字段
        update_data = request.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(session, field):
                setattr(session, field, value)

        await db.commit()
        await db.refresh(session)

        return {
            "message": "会话更新成功",
            "data": session.to_dict()
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新会话失败: {str(e)}")
        raise HTTPException(status_code=500, detail="更新会话失败")

@router.delete("/sessions/{session_id}", summary="删除会话")
async def delete_session(session_id: str, db: AsyncSession = Depends(get_db)):
    """删除指定的会话记录"""
    try:
        query = select(OllamaSession).where(OllamaSession.session_id == session_id)
        result = await db.execute(query)
        session = result.scalar_one_or_none()

        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")

        await db.delete(session)
        await db.commit()

        return {"message": "会话删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除会话失败: {str(e)}")
        raise HTTPException(status_code=500, detail="删除会话失败")

# ============= 任务管理端点 =============
@router.get("/tasks", response_model=PaginatedResponse, summary="获取任务列表")
async def get_tasks(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    status: Optional[TaskStatus] = Query(None, description="状态筛选"),
    task_type: Optional[TaskType] = Query(None, description="任务类型筛选"),
    model_name: Optional[str] = Query(None, description="模型筛选"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向"),
    db: AsyncSession = Depends(get_db)
):
    """获取任务列表，支持分页、搜索和筛选"""
    try:
        # 构建查询
        query = select(Task)
        count_query = select(func.count(Task.id))

        # 搜索筛选
        if search:
            search_filter = or_(
                Task.task_id.contains(search),
                Task.model_name.contains(search),
                Task.session_id.contains(search),
                Task.error_message.contains(search)
            )
            query = query.where(search_filter)
            count_query = count_query.where(search_filter)

        # 状态筛选
        if status:
            status_filter = Task.status == status
            query = query.where(status_filter)
            count_query = count_query.where(status_filter)

        # 任务类型筛选
        if task_type:
            type_filter = Task.task_type == task_type
            query = query.where(type_filter)
            count_query = count_query.where(type_filter)

        # 模型筛选
        if model_name:
            model_filter = Task.model_name == model_name
            query = query.where(model_filter)
            count_query = count_query.where(model_filter)

        # 排序
        if hasattr(Task, sort_by):
            sort_column = getattr(Task, sort_by)
            if sort_order.lower() == "asc":
                query = query.order_by(asc(sort_column))
            else:
                query = query.order_by(desc(sort_column))

        # 分页
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)

        # 执行查询
        total_result = await db.execute(count_query)
        total = total_result.scalar() or 0

        result = await db.execute(query)
        tasks = result.scalars().all()

        # 转换为字典
        data = [task.to_dict() for task in tasks]

        total_pages = (total + page_size - 1) // page_size

        return PaginatedResponse(
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            data=data
        )

    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取任务列表失败")

@router.get("/tasks/{task_id}", summary="获取任务详情")
async def get_task(task_id: str, db: AsyncSession = Depends(get_db)):
    """获取指定任务的详细信息"""
    try:
        query = select(Task).where(Task.task_id == task_id)
        result = await db.execute(query)
        task = result.scalar_one_or_none()

        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        return task.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取任务详情失败")

@router.post("/tasks", summary="创建任务")
async def create_task(
    request: CreateTaskRequest,
    db: AsyncSession = Depends(get_db)
):
    """创建新的任务记录"""
    try:
        task_id = str(uuid.uuid4())

        task = Task(
            task_id=task_id,
            task_type=request.task_type,
            model_name=request.model_name,
            request_data=request.request_data,
            session_id=request.session_id
        )

        db.add(task)
        await db.commit()
        await db.refresh(task)

        return {
            "message": "任务创建成功",
            "task_id": task_id,
            "data": task.to_dict()
        }

    except Exception as e:
        await db.rollback()
        logger.error(f"创建任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建任务失败")

@router.put("/tasks/{task_id}", summary="更新任务")
async def update_task(
    task_id: str,
    request: UpdateTaskRequest,
    db: AsyncSession = Depends(get_db)
):
    """更新指定任务的信息"""
    try:
        query = select(Task).where(Task.task_id == task_id)
        result = await db.execute(query)
        task = result.scalar_one_or_none()

        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 更新字段
        update_data = request.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(task, field):
                setattr(task, field, value)

        await db.commit()
        await db.refresh(task)

        return {
            "message": "任务更新成功",
            "data": task.to_dict()
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail="更新任务失败")

@router.delete("/tasks/{task_id}", summary="删除任务")
async def delete_task(task_id: str, db: AsyncSession = Depends(get_db)):
    """删除指定的任务记录"""
    try:
        query = select(Task).where(Task.task_id == task_id)
        result = await db.execute(query)
        task = result.scalar_one_or_none()

        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        await db.delete(task)
        await db.commit()

        return {"message": "任务删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail="删除任务失败")

# ============= 内存统计管理端点 =============
@router.get("/memory-stats", response_model=PaginatedResponse, summary="获取内存统计列表")
async def get_memory_stats(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    sort_by: str = Query("timestamp", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向"),
    db: AsyncSession = Depends(get_db)
):
    """获取内存统计列表，支持分页和日期筛选"""
    try:
        # 构建查询
        query = select(MemoryStat)
        count_query = select(func.count(MemoryStat.id))

        # 日期筛选
        if start_date:
            start_filter = MemoryStat.timestamp >= start_date
            query = query.where(start_filter)
            count_query = count_query.where(start_filter)

        if end_date:
            end_filter = MemoryStat.timestamp <= end_date + " 23:59:59"
            query = query.where(end_filter)
            count_query = count_query.where(end_filter)

        # 排序
        if hasattr(MemoryStat, sort_by):
            sort_column = getattr(MemoryStat, sort_by)
            if sort_order.lower() == "asc":
                query = query.order_by(asc(sort_column))
            else:
                query = query.order_by(desc(sort_column))

        # 分页
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)

        # 执行查询
        total_result = await db.execute(count_query)
        total = total_result.scalar() or 0

        result = await db.execute(query)
        stats = result.scalars().all()

        # 转换为字典
        data = [stat.to_dict() for stat in stats]

        total_pages = (total + page_size - 1) // page_size

        return PaginatedResponse(
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            data=data
        )

    except Exception as e:
        logger.error(f"获取内存统计列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取内存统计列表失败")

@router.post("/memory-stats", summary="创建内存统计")
async def create_memory_stat(
    request: CreateMemoryStatRequest,
    db: AsyncSession = Depends(get_db)
):
    """创建新的内存统计记录"""
    try:
        stat = MemoryStat(
            total_memory=request.total_memory,
            available_memory=request.available_memory,
            used_memory=request.used_memory,
            memory_percent=request.memory_percent,
            cpu_percent=request.cpu_percent,
            active_sessions=request.active_sessions,
            queue_length=request.queue_length
        )

        db.add(stat)
        await db.commit()
        await db.refresh(stat)

        return {
            "message": "内存统计创建成功",
            "stat_id": stat.id,
            "data": stat.to_dict()
        }

    except Exception as e:
        await db.rollback()
        logger.error(f"创建内存统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建内存统计失败")

@router.delete("/memory-stats/{stat_id}", summary="删除内存统计")
async def delete_memory_stat(stat_id: int, db: AsyncSession = Depends(get_db)):
    """删除指定的内存统计记录"""
    try:
        query = select(MemoryStat).where(MemoryStat.id == stat_id)
        result = await db.execute(query)
        stat = result.scalar_one_or_none()

        if not stat:
            raise HTTPException(status_code=404, detail="内存统计记录不存在")

        await db.delete(stat)
        await db.commit()

        return {"message": "内存统计删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除内存统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail="删除内存统计失败")

# ============= 测试计划记录管理端点 =============
@router.get("/test-plan-records", response_model=PaginatedResponse, summary="获取测试计划记录列表")
async def get_test_plan_records_db(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    platform: Optional[str] = Query(None, description="平台筛选"),
    status: Optional[TestPlanStatus] = Query(None, description="状态筛选"),
    model_name: Optional[str] = Query(None, description="模型筛选"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向"),
    db: AsyncSession = Depends(get_db)
):
    """获取测试计划记录列表，支持分页、搜索和筛选"""
    try:
        # 构建查询
        query = select(TestPlanRecord)
        count_query = select(func.count(TestPlanRecord.id))

        # 搜索筛选
        if search:
            search_filter = or_(
                TestPlanRecord.record_id.contains(search),
                TestPlanRecord.original_instruction.contains(search),
                TestPlanRecord.plan_summary.contains(search),
                TestPlanRecord.plan_id.contains(search)
            )
            query = query.where(search_filter)
            count_query = count_query.where(search_filter)

        # 平台筛选
        if platform:
            platform_filter = TestPlanRecord.detected_platform == platform.lower()
            query = query.where(platform_filter)
            count_query = count_query.where(platform_filter)

        # 状态筛选
        if status:
            status_filter = TestPlanRecord.status == status
            query = query.where(status_filter)
            count_query = count_query.where(status_filter)

        # 模型筛选
        if model_name:
            model_filter = TestPlanRecord.model_name == model_name
            query = query.where(model_filter)
            count_query = count_query.where(model_filter)

        # 排序
        if hasattr(TestPlanRecord, sort_by):
            sort_column = getattr(TestPlanRecord, sort_by)
            if sort_order.lower() == "asc":
                query = query.order_by(asc(sort_column))
            else:
                query = query.order_by(desc(sort_column))

        # 分页
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)

        # 执行查询
        total_result = await db.execute(count_query)
        total = total_result.scalar() or 0

        result = await db.execute(query)
        records = result.scalars().all()

        # 转换为字典
        data = [record.to_dict() for record in records]

        total_pages = (total + page_size - 1) // page_size

        return PaginatedResponse(
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            data=data
        )

    except Exception as e:
        logger.error(f"获取测试计划记录列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取测试计划记录列表失败")

@router.post("/test-plan-records", summary="创建测试计划记录")
async def create_test_plan_record(
    request: CreateTestPlanRequest,
    db: AsyncSession = Depends(get_db)
):
    """创建新的测试计划记录"""
    try:
        record_id = str(uuid.uuid4())

        record = TestPlanRecord(
            record_id=record_id,
            original_instruction=request.original_instruction,
            target_platform=request.target_platform,
            detected_platform=request.detected_platform,
            model_name=request.model_name,
            status=request.status,
            generated_plan=request.generated_plan,
            plan_id=request.plan_id,
            plan_summary=request.plan_summary,
            total_steps=request.total_steps,
            error_message=request.error_message,
            raw_response=request.raw_response,
            processing_time=request.processing_time,
            prompt_tokens=request.prompt_tokens,
            response_length=request.response_length,
            client_ip=request.client_ip,
            user_agent=request.user_agent
        )

        db.add(record)
        await db.commit()
        await db.refresh(record)

        return {
            "message": "测试计划记录创建成功",
            "record_id": record_id,
            "data": record.to_dict()
        }

    except Exception as e:
        await db.rollback()
        logger.error(f"创建测试计划记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建测试计划记录失败")

@router.delete("/test-plan-records/{record_id}", summary="删除测试计划记录")
async def delete_test_plan_record(record_id: str, db: AsyncSession = Depends(get_db)):
    """删除指定的测试计划记录"""
    try:
        query = select(TestPlanRecord).where(TestPlanRecord.record_id == record_id)
        result = await db.execute(query)
        record = result.scalar_one_or_none()

        if not record:
            raise HTTPException(status_code=404, detail="测试计划记录不存在")

        await db.delete(record)
        await db.commit()

        return {"message": "测试计划记录删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除测试计划记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail="删除测试计划记录失败")
