import logging
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import time

from app.config import config
from app.utils.logging_config import setup_logging
from app.models.database import init_database, close_database
from app.api.ollama import router as ollama_router
from app.api.test_plan import router as test_plan_router
from app.api.frontend import router as frontend_router
from app.api.database import router as database_router
from app.services.ollama_proxy import ollama_proxy
from app.services.memory_monitor import memory_monitor
from app.services.task_queue import task_queue
from app.services.session_manager import session_manager

# 设置日志
loggers = setup_logging()
logger = loggers['app']
api_logger = loggers['api']

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("正在启动 Overall API Server...")
    
    try:
        # 确保目录存在
        config.ensure_directories()
        
        # 初始化数据库
        await init_database()
        logger.info("数据库初始化完成")
        
        # 检查ollama服务
        if await ollama_proxy.check_health():
            logger.info(f"Ollama服务连接成功: {config.ollama_base_url}")
        else:
            logger.warning(f"无法连接到Ollama服务: {config.ollama_base_url}")
        
        # 启动内存监控
        await memory_monitor.start()
        logger.info("内存监控服务已启动")
        
        # 启动任务队列
        await task_queue.start()
        logger.info("任务队列服务已启动")
        
        # 启动会话管理器
        await session_manager.start()
        logger.info("会话管理器已启动")
        
        logger.info("Overall API Server 启动完成!")
        yield
        
    except Exception as e:
        logger.error(f"启动失败: {e}")
        raise
    finally:
        logger.info("正在关闭 Overall API Server...")
        
        # 停止会话管理器
        await session_manager.stop()
        
        # 停止任务队列
        await task_queue.stop()
        
        # 停止内存监控
        await memory_monitor.stop()
        
        # 关闭数据库连接
        await close_database()
        
        # 关闭ollama代理
        await ollama_proxy.close()
        
        logger.info("Overall API Server 已关闭")

# 创建FastAPI应用
app = FastAPI(
    title="Overall API Server",
    description="本地服务聚合API服务器，提供ollama模型代理和智能队列管理",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS中间件配置，支持局域网访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录API请求日志"""
    start_time = time.time()
    
    # 记录请求信息
    client_ip = request.client.host if request.client else "unknown"
    api_logger.info(f"请求开始 - {request.method} {request.url} - IP: {client_ip}")
    
    try:
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 记录响应信息
        api_logger.info(
            f"请求完成 - {request.method} {request.url} - "
            f"状态码: {response.status_code} - "
            f"耗时: {process_time:.3f}s - "
            f"IP: {client_ip}"
        )
        
        # 添加响应头
        response.headers["X-Process-Time"] = str(process_time)
        response.headers["X-Server"] = "overall-api-server"
        
        return response
        
    except Exception as e:
        process_time = time.time() - start_time
        api_logger.error(
            f"请求异常 - {request.method} {request.url} - "
            f"错误: {str(e)} - "
            f"耗时: {process_time:.3f}s - "
            f"IP: {client_ip}"
        )
        raise

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"全局异常 - {request.method} {request.url} - {str(exc)}")
    
    if isinstance(exc, HTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.detail,
                "status_code": exc.status_code,
                "path": str(request.url)
            }
        )
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "status_code": 500,
            "path": str(request.url)
        }
    )

# 注册路由
app.include_router(ollama_router)
app.include_router(test_plan_router)
app.include_router(frontend_router)
app.include_router(database_router)

# 根路径
@app.get("/", summary="服务信息")
async def root():
    """根路径，返回服务信息"""
    return {
        "service": "Overall API Server",
        "version": "1.0.0",
        "status": "running",
        "description": "本地服务聚合API服务器",
        "endpoints": {
            "ollama": "/ollama",
            "test_plan": "/test-plan",
            "frontend": "/ui",
            "docs": "/docs",
            "redoc": "/redoc"
        },
        "config": {
            "ollama_backend": config.ollama_base_url,
            "memory_threshold": f"{config.memory_threshold * 100}%",
            "server_port": config.server_port
        }
    }

@app.get("/health", summary="健康检查")
async def health_check():
    """健康检查端点"""
    try:
        # 检查ollama服务
        ollama_healthy = await ollama_proxy.check_health()
        
        # 获取内存状态
        memory_stats = memory_monitor.get_current_stats()
        memory_pressure = memory_monitor.is_memory_pressure()
        
        # 获取队列状态
        queue_status = await task_queue.get_queue_status()
        
        # 获取会话状态
        session_stats = await session_manager.get_session_stats()
        
        overall_status = "healthy"
        if not ollama_healthy:
            overall_status = "degraded"
        elif memory_pressure:
            overall_status = "under_pressure"
        
        # 计算系统负载（基于内存使用率和队列长度）
        memory_percent = memory_stats.get('memory_percent', 0) * 100 if memory_stats else 0
        queue_length = queue_status.get('total_tasks', 0) if queue_status else 0
        
        # 简单的负载计算：内存权重70%，队列权重30%
        system_load = min((memory_percent * 0.7) + (min(queue_length * 10, 100) * 0.3), 100)
        
        return {
            "status": overall_status,
            "services": {
                "ollama": "healthy" if ollama_healthy else "unhealthy",
                "database": "healthy",
                "api_server": "healthy",
                "memory_monitor": "healthy",
                "task_queue": "healthy",
                "session_manager": "healthy"
            },
            "system": {
                "load_percent": round(system_load, 1),
                "memory": {
                    "usage_percent": memory_percent,
                    "under_pressure": memory_pressure,
                    "threshold_percent": config.memory_threshold * 100
                },
                "queue": queue_status,
                "sessions": session_stats
            },
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }
        )