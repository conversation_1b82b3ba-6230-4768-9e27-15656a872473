from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import MetaData
from app.config import config
import logging

# 数据库基类
Base = declarative_base()
metadata = MetaData()

# 全局数据库引擎和会话
engine = None
async_session = None

logger = logging.getLogger(__name__)

async def init_database():
    """初始化数据库连接"""
    global engine, async_session
    
    try:
        # 创建异步数据库引擎
        engine = create_async_engine(
            config.database_url,
            echo=config.get('database.echo', False),
            future=True
        )
        
        # 创建异步会话工厂
        async_session = async_sessionmaker(
            engine, 
            class_=AsyncSession, 
            expire_on_commit=False
        )
        
        # 创建所有表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("数据库初始化成功")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise

def get_db_session():
    """获取数据库会话生成器"""
    async def _session_generator():
        if async_session is None:
            raise RuntimeError("数据库未初始化")
        
        async with async_session() as session:
            try:
                yield session
            except Exception as e:
                await session.rollback()
                logger.error(f"数据库会话错误: {e}")
                raise
            finally:
                await session.close()
    
    return _session_generator()

async def close_database():
    """关闭数据库连接"""
    global engine
    if engine:
        await engine.dispose()
        logger.info("数据库连接已关闭")