from sqlalchemy import Column, Integer, String, Float, DateTime, JSON, Enum, Boolean
from sqlalchemy.sql import func
from app.models.database import Base
from datetime import datetime
from typing import Dict, Any
import enum

class SessionStatus(enum.Enum):
    """会话状态枚举"""
    IDLE = "idle"          # 空闲，模型已加载
    BUSY = "busy"          # 正在处理任务
    LOADING = "loading"    # 正在加载模型
    ERROR = "error"        # 错误状态
    TERMINATED = "terminated"  # 已终止

class OllamaSession(Base):
    """Ollama会话窗口模型"""
    __tablename__ = "ollama_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(36), unique=True, index=True, nullable=False)  # UUID
    
    # 会话基本信息
    model_name = Column(String(255), nullable=False, index=True)
    status = Column(Enum(SessionStatus), default=SessionStatus.LOADING, nullable=False)
    
    # 进程信息
    process_id = Column(Integer, nullable=True)  # ollama进程ID（如果有）
    port = Column(Integer, nullable=True)  # 如果使用独立端口
    
    # 性能信息
    memory_usage = Column(Float, nullable=True)  # 会话内存使用量(GB)
    model_size = Column(Float, nullable=True)  # 模型大小(GB)
    load_time = Column(Float, nullable=True)  # 模型加载时间(秒)
    
    # 使用统计
    total_requests = Column(Integer, default=0)
    successful_requests = Column(Integer, default=0)
    failed_requests = Column(Integer, default=0)
    total_tokens = Column(Integer, default=0)
    
    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_used_at = Column(DateTime(timezone=True), server_default=func.now())
    last_heartbeat = Column(DateTime(timezone=True), server_default=func.now())
    terminated_at = Column(DateTime(timezone=True), nullable=True)
    
    # 配置信息
    max_context_length = Column(Integer, nullable=True)
    temperature = Column(Float, nullable=True)
    session_config = Column(JSON, nullable=True)  # 额外配置
    
    # 健康检查
    is_healthy = Column(Boolean, default=True)
    error_count = Column(Integer, default=0)
    last_error = Column(String(500), nullable=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'session_id': self.session_id,
            'model_name': self.model_name,
            'status': self.status.value if self.status else None,
            'process_id': self.process_id,
            'port': self.port,
            'memory_usage': self.memory_usage,
            'model_size': self.model_size,
            'load_time': self.load_time,
            'total_requests': self.total_requests,
            'successful_requests': self.successful_requests,
            'failed_requests': self.failed_requests,
            'total_tokens': self.total_tokens,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_used_at': self.last_used_at.isoformat() if self.last_used_at else None,
            'last_heartbeat': self.last_heartbeat.isoformat() if self.last_heartbeat else None,
            'terminated_at': self.terminated_at.isoformat() if self.terminated_at else None,
            'is_healthy': self.is_healthy,
            'error_count': self.error_count,
            'last_error': self.last_error
        }
    
    def update_stats(self, success: bool = True, tokens: int = 0):
        """更新使用统计"""
        self.total_requests += 1
        self.total_tokens += tokens
        self.last_used_at = datetime.now()
        
        if success:
            self.successful_requests += 1
            self.error_count = 0  # 重置错误计数
        else:
            self.failed_requests += 1
            self.error_count += 1
    
    def is_idle_timeout(self, timeout_seconds: int) -> bool:
        """检查是否超过空闲时间"""
        if not self.last_used_at:
            return False
        
        idle_time = (datetime.now() - self.last_used_at).total_seconds()
        return idle_time > timeout_seconds
    
    def should_terminate(self, error_threshold: int = 5) -> bool:
        """判断是否应该终止会话"""
        return (
            self.error_count >= error_threshold or
            self.status == SessionStatus.ERROR
        )