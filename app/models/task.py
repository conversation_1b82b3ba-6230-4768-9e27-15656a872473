from sqlalchemy import Column, Integer, String, Float, Text, DateTime, JSON, Enum
from sqlalchemy.sql import func
from app.models.database import Base
from datetime import datetime
from typing import Dict, Any, Optional
import enum

class TaskStatus(enum.Enum):
    """任务状态枚举"""
    PENDING = "pending"
    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskType(enum.Enum):
    """任务类型枚举"""
    GENERATE = "generate"
    CHAT = "chat"
    TEST_PLAN = "test_plan"

class TestPlanStatus(enum.Enum):
    """测试计划状态枚举"""
    SUCCESS = "success"
    FAILED = "failed"
    ERROR = "error"

class Task(Base):
    """任务模型"""
    __tablename__ = "tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(36), unique=True, index=True, nullable=False)  # UUID
    task_type = Column(Enum(TaskType), nullable=False)
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING, nullable=False)
    
    # 请求参数
    model_name = Column(String(255), nullable=False)
    request_data = Column(JSON, nullable=False)  # 原始请求参数
    
    # 执行信息
    session_id = Column(String(36), nullable=True, index=True)  # 关联的会话ID
    queue_position = Column(Integer, nullable=True)
    estimated_wait_time = Column(Float, nullable=True)  # 预估等待时间(秒)
    
    # 结果信息
    response_data = Column(Text, nullable=True)  # 响应数据
    error_message = Column(Text, nullable=True)
    
    # 性能统计
    queue_time = Column(Float, nullable=True)  # 排队时间(秒)
    process_time = Column(Float, nullable=True)  # 处理时间(秒)
    total_tokens = Column(Integer, nullable=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'task_type': self.task_type.value if self.task_type else None,
            'status': self.status.value if self.status else None,
            'model_name': self.model_name,
            'session_id': self.session_id,
            'queue_position': self.queue_position,
            'estimated_wait_time': self.estimated_wait_time,
            'error_message': self.error_message,
            'queue_time': self.queue_time,
            'process_time': self.process_time,
            'total_tokens': self.total_tokens,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
        }

class MemoryStat(Base):
    """内存监控统计"""
    __tablename__ = "memory_stats"
    
    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    
    # 内存信息
    total_memory = Column(Float, nullable=False)  # 总内存 (GB)
    available_memory = Column(Float, nullable=False)  # 可用内存 (GB)
    used_memory = Column(Float, nullable=False)  # 已用内存 (GB)
    memory_percent = Column(Float, nullable=False)  # 内存使用百分比
    
    # 系统负载
    cpu_percent = Column(Float, nullable=True)
    active_sessions = Column(Integer, default=0)
    queue_length = Column(Integer, default=0)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'total_memory': self.total_memory,
            'available_memory': self.available_memory,
            'used_memory': self.used_memory,
            'memory_percent': self.memory_percent,
            'cpu_percent': self.cpu_percent,
            'active_sessions': self.active_sessions,
            'queue_length': self.queue_length
        }

class TestPlanRecord(Base):
    """测试计划记录模型"""
    __tablename__ = "test_plan_records"
    
    id = Column(Integer, primary_key=True, index=True)
    record_id = Column(String(36), unique=True, index=True, nullable=False)  # UUID
    
    # 请求信息
    original_instruction = Column(Text, nullable=False)  # 原始自然语言指令
    target_platform = Column(String(20), nullable=True)  # ios/android
    detected_platform = Column(String(20), nullable=False)  # 实际检测到的平台
    model_name = Column(String(255), nullable=False)  # 使用的模型
    
    # 生成结果
    status = Column(Enum(TestPlanStatus), nullable=False)
    generated_plan = Column(JSON, nullable=True)  # 生成的测试计划JSON
    plan_id = Column(String(100), nullable=True, index=True)  # 计划ID（来自生成结果）
    plan_summary = Column(Text, nullable=True)  # 计划摘要
    total_steps = Column(Integer, nullable=True)  # 总步骤数
    
    # 错误信息
    error_message = Column(Text, nullable=True)
    raw_response = Column(Text, nullable=True)  # 模型原始响应
    
    # 性能统计
    processing_time = Column(Float, nullable=True)  # 处理耗时（秒）
    prompt_tokens = Column(Integer, nullable=True)  # prompt token数量（预估）
    response_length = Column(Integer, nullable=True)  # 响应长度（字符数）
    
    # 客户端信息
    client_ip = Column(String(45), nullable=True)  # 客户端IP
    user_agent = Column(String(500), nullable=True)  # 用户代理
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'record_id': self.record_id,
            'original_instruction': self.original_instruction,
            'target_platform': self.target_platform,
            'detected_platform': self.detected_platform,
            'model_name': self.model_name,
            'status': self.status.value if self.status else None,
            'generated_plan': self.generated_plan,
            'plan_id': self.plan_id,
            'plan_summary': self.plan_summary,
            'total_steps': self.total_steps,
            'error_message': self.error_message,
            'raw_response': self.raw_response,
            'processing_time': self.processing_time,
            'prompt_tokens': self.prompt_tokens,
            'response_length': self.response_length,
            'client_ip': self.client_ip,
            'user_agent': self.user_agent,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
    
    def to_summary_dict(self) -> Dict[str, Any]:
        """转换为摘要字典（不包含完整计划和原始响应）"""
        return {
            'record_id': self.record_id,
            'original_instruction': self.original_instruction[:100] + '...' if len(self.original_instruction) > 100 else self.original_instruction,
            'detected_platform': self.detected_platform,
            'status': self.status.value if self.status else None,
            'plan_id': self.plan_id,
            'plan_summary': self.plan_summary,
            'total_steps': self.total_steps,
            'processing_time': self.processing_time,
            'created_at': self.created_at.isoformat() if self.created_at else None,
        }