2025-08-20 19:40:48,452 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 19:40:48,453 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 19:40:48,453 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 19:40:48,453 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 19:40:49,537 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:40:49,537 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:40:49,567 - app.models.database - ERROR - 数据库初始化失败: the greenlet library is required to use this function. No module named 'greenlet'
2025-08-20 19:40:49,567 - app.models.database - ERROR - 数据库初始化失败: the greenlet library is required to use this function. No module named 'greenlet'
2025-08-20 19:40:49,567 - app - ERROR - 启动失败: the greenlet library is required to use this function. No module named 'greenlet'
2025-08-20 19:40:49,567 - app - ERROR - 启动失败: the greenlet library is required to use this function. No module named 'greenlet'
2025-08-20 19:40:49,567 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 19:40:49,567 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 19:40:49,569 - app - INFO - Overall API Server 已关闭
2025-08-20 19:40:49,569 - app - INFO - Overall API Server 已关闭
2025-08-20 19:41:12,913 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 19:41:12,913 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 19:41:12,913 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 19:41:12,913 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 19:41:13,706 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:41:13,706 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:41:13,717 - app.models.database - INFO - 数据库初始化成功
2025-08-20 19:41:13,717 - app.models.database - INFO - 数据库初始化成功
2025-08-20 19:41:13,717 - app - INFO - 数据库初始化完成
2025-08-20 19:41:13,717 - app - INFO - 数据库初始化完成
2025-08-20 19:41:13,810 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 502 Bad Gateway"
2025-08-20 19:41:13,810 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 502 Bad Gateway"
2025-08-20 19:41:13,810 - app - WARNING - 无法连接到Ollama服务: http://localhost:11434
2025-08-20 19:41:13,810 - app - WARNING - 无法连接到Ollama服务: http://localhost:11434
2025-08-20 19:41:13,810 - app - INFO - Overall API Server 启动完成!
2025-08-20 19:41:13,810 - app - INFO - Overall API Server 启动完成!
2025-08-20 19:41:30,303 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 502 Bad Gateway"
2025-08-20 19:41:30,303 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 502 Bad Gateway"
2025-08-20 19:41:39,086 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 502 Bad Gateway"
2025-08-20 19:41:39,086 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 502 Bad Gateway"
2025-08-20 19:41:39,088 - app.services.ollama_proxy - ERROR - 获取模型列表失败: Server error '502 Bad Gateway' for url 'http://localhost:11434/api/tags'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/502
2025-08-20 19:41:39,088 - app.services.ollama_proxy - ERROR - 获取模型列表失败: Server error '502 Bad Gateway' for url 'http://localhost:11434/api/tags'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/502
2025-08-20 19:42:01,659 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 19:42:01,659 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 19:42:01,659 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 19:42:01,659 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 19:42:02,100 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:42:02,100 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:42:02,110 - app.models.database - INFO - 数据库初始化成功
2025-08-20 19:42:02,110 - app.models.database - INFO - 数据库初始化成功
2025-08-20 19:42:02,110 - app - INFO - 数据库初始化完成
2025-08-20 19:42:02,110 - app - INFO - 数据库初始化完成
2025-08-20 19:42:02,200 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 502 Bad Gateway"
2025-08-20 19:42:02,200 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 502 Bad Gateway"
2025-08-20 19:42:02,200 - app - WARNING - 无法连接到Ollama服务: http://localhost:11434
2025-08-20 19:42:02,200 - app - WARNING - 无法连接到Ollama服务: http://localhost:11434
2025-08-20 19:42:02,200 - app - INFO - Overall API Server 启动完成!
2025-08-20 19:42:02,200 - app - INFO - Overall API Server 启动完成!
2025-08-20 19:42:02,202 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 19:42:02,202 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 19:42:02,202 - app.models.database - INFO - 数据库连接已关闭
2025-08-20 19:42:02,202 - app.models.database - INFO - 数据库连接已关闭
2025-08-20 19:42:02,202 - app - INFO - Overall API Server 已关闭
2025-08-20 19:42:02,202 - app - INFO - Overall API Server 已关闭
2025-08-20 19:42:20,057 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 19:42:20,057 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 19:42:20,057 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 19:42:20,057 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 19:42:20,394 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:42:20,394 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:42:20,402 - app.models.database - INFO - 数据库初始化成功
2025-08-20 19:42:20,402 - app.models.database - INFO - 数据库初始化成功
2025-08-20 19:42:20,402 - app - INFO - 数据库初始化完成
2025-08-20 19:42:20,402 - app - INFO - 数据库初始化完成
2025-08-20 19:42:20,486 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 502 Bad Gateway"
2025-08-20 19:42:20,486 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 502 Bad Gateway"
2025-08-20 19:42:20,486 - app - WARNING - 无法连接到Ollama服务: http://localhost:11434
2025-08-20 19:42:20,486 - app - WARNING - 无法连接到Ollama服务: http://localhost:11434
2025-08-20 19:42:20,486 - app - INFO - Overall API Server 启动完成!
2025-08-20 19:42:20,486 - app - INFO - Overall API Server 启动完成!
2025-08-20 19:42:27,889 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 502 Bad Gateway"
2025-08-20 19:42:27,889 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 502 Bad Gateway"
2025-08-20 19:42:27,890 - app.services.ollama_proxy - ERROR - 获取模型列表失败: Server error '502 Bad Gateway' for url 'http://localhost:11434/api/tags'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/502
2025-08-20 19:42:27,890 - app.services.ollama_proxy - ERROR - 获取模型列表失败: Server error '502 Bad Gateway' for url 'http://localhost:11434/api/tags'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/502
2025-08-20 19:43:26,626 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 19:43:26,626 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 19:43:26,626 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 19:43:26,626 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 19:43:27,058 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:43:27,058 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:43:27,070 - app.models.database - INFO - 数据库初始化成功
2025-08-20 19:43:27,070 - app.models.database - INFO - 数据库初始化成功
2025-08-20 19:43:27,071 - app - INFO - 数据库初始化完成
2025-08-20 19:43:27,071 - app - INFO - 数据库初始化完成
2025-08-20 19:43:27,102 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:43:27,102 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:43:27,102 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 19:43:27,102 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 19:43:27,102 - app - INFO - Overall API Server 启动完成!
2025-08-20 19:43:27,102 - app - INFO - Overall API Server 启动完成!
2025-08-20 19:43:27,103 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 19:43:27,103 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 19:43:27,104 - app.models.database - INFO - 数据库连接已关闭
2025-08-20 19:43:27,104 - app.models.database - INFO - 数据库连接已关闭
2025-08-20 19:43:27,104 - app - INFO - Overall API Server 已关闭
2025-08-20 19:43:27,104 - app - INFO - Overall API Server 已关闭
2025-08-20 19:43:42,211 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 19:43:42,211 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 19:43:42,211 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 19:43:42,211 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 19:43:42,714 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:43:42,714 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:43:42,726 - app.models.database - INFO - 数据库初始化成功
2025-08-20 19:43:42,726 - app.models.database - INFO - 数据库初始化成功
2025-08-20 19:43:42,726 - app - INFO - 数据库初始化完成
2025-08-20 19:43:42,726 - app - INFO - 数据库初始化完成
2025-08-20 19:43:42,735 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:43:42,735 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:43:42,736 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 19:43:42,736 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 19:43:42,736 - app - INFO - Overall API Server 启动完成!
2025-08-20 19:43:42,736 - app - INFO - Overall API Server 启动完成!
2025-08-20 19:43:48,587 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:43:48,587 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:43:54,252 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:43:54,252 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:43:54,252 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: False, 任务ID: d2eafc8f-87e9-44e3-a539-a80b704bfcaf
2025-08-20 19:43:54,252 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: False, 任务ID: d2eafc8f-87e9-44e3-a539-a80b704bfcaf
2025-08-20 19:43:54,252 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: d2eafc8f-87e9-44e3-a539-a80b704bfcaf
2025-08-20 19:43:54,252 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: d2eafc8f-87e9-44e3-a539-a80b704bfcaf
2025-08-20 19:44:03,272 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 19:44:03,272 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 19:44:03,273 - app.services.ollama_proxy - INFO - 非流式generate请求完成 - 任务ID: d2eafc8f-87e9-44e3-a539-a80b704bfcaf
2025-08-20 19:44:03,273 - app.services.ollama_proxy - INFO - 非流式generate请求完成 - 任务ID: d2eafc8f-87e9-44e3-a539-a80b704bfcaf
2025-08-20 19:44:13,565 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:44:13,565 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:44:13,566 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: True, 任务ID: 31f0da90-8c6f-454f-8dce-28e9c84b8857
2025-08-20 19:44:13,566 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: True, 任务ID: 31f0da90-8c6f-454f-8dce-28e9c84b8857
2025-08-20 19:44:13,566 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 31f0da90-8c6f-454f-8dce-28e9c84b8857
2025-08-20 19:44:13,566 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 31f0da90-8c6f-454f-8dce-28e9c84b8857
2025-08-20 19:44:14,505 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 19:44:14,505 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 19:47:05,591 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 19:47:05,591 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 19:47:05,591 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 19:47:05,591 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 19:47:06,125 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:47:06,125 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:47:06,141 - app.models.database - INFO - 数据库初始化成功
2025-08-20 19:47:06,141 - app.models.database - INFO - 数据库初始化成功
2025-08-20 19:47:06,142 - app - INFO - 数据库初始化完成
2025-08-20 19:47:06,142 - app - INFO - 数据库初始化完成
2025-08-20 19:47:06,162 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:47:06,162 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:47:06,163 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 19:47:06,163 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 19:47:06,163 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 19:47:06,163 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 19:47:06,163 - app - INFO - 内存监控服务已启动
2025-08-20 19:47:06,163 - app - INFO - 内存监控服务已启动
2025-08-20 19:47:06,163 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 19:47:06,163 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 19:47:06,163 - app - INFO - 任务队列服务已启动
2025-08-20 19:47:06,163 - app - INFO - 任务队列服务已启动
2025-08-20 19:47:06,163 - app - INFO - Overall API Server 启动完成!
2025-08-20 19:47:06,163 - app - INFO - Overall API Server 启动完成!
2025-08-20 19:47:07,166 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:47:07,166 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:47:07,167 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 19:47:07,167 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 19:47:07,167 - app.services.task_queue - INFO - 任务队列处理器已停止
2025-08-20 19:47:07,167 - app.services.task_queue - INFO - 任务队列处理器已停止
2025-08-20 19:47:07,167 - app.services.memory_monitor - INFO - 内存监控服务已停止
2025-08-20 19:47:07,167 - app.services.memory_monitor - INFO - 内存监控服务已停止
2025-08-20 19:47:07,168 - app.models.database - INFO - 数据库连接已关闭
2025-08-20 19:47:07,168 - app.models.database - INFO - 数据库连接已关闭
2025-08-20 19:47:07,168 - app - INFO - Overall API Server 已关闭
2025-08-20 19:47:07,168 - app - INFO - Overall API Server 已关闭
2025-08-20 19:49:16,455 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 19:49:16,455 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 19:49:16,455 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 19:49:16,455 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 19:49:16,946 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:49:16,946 - app - INFO - 正在启动 Overall API Server...
2025-08-20 19:49:16,957 - app.models.database - INFO - 数据库初始化成功
2025-08-20 19:49:16,957 - app.models.database - INFO - 数据库初始化成功
2025-08-20 19:49:16,958 - app - INFO - 数据库初始化完成
2025-08-20 19:49:16,958 - app - INFO - 数据库初始化完成
2025-08-20 19:49:16,975 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:49:16,975 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:49:16,975 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 19:49:16,975 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 19:49:16,975 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 19:49:16,975 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 19:49:16,976 - app - INFO - 内存监控服务已启动
2025-08-20 19:49:16,976 - app - INFO - 内存监控服务已启动
2025-08-20 19:49:16,976 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 19:49:16,976 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 19:49:16,976 - app - INFO - 任务队列服务已启动
2025-08-20 19:49:16,976 - app - INFO - 任务队列服务已启动
2025-08-20 19:49:16,976 - app - INFO - Overall API Server 启动完成!
2025-08-20 19:49:16,976 - app - INFO - Overall API Server 启动完成!
2025-08-20 19:49:17,980 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:49:17,980 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:49:23,987 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:49:23,987 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:49:26,723 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:49:26,723 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:49:29,988 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:49:29,988 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:49:35,995 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:49:35,995 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:49:41,999 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:49:41,999 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:49:48,005 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:49:48,005 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:49:54,011 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:49:54,011 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:00,018 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:00,018 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:06,024 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:06,024 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:12,030 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:12,030 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:18,036 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:18,036 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:24,040 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:24,040 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:30,047 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:30,047 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:36,054 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:36,054 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:42,058 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:42,058 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:48,065 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:48,065 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:54,071 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:50:54,071 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:00,074 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:00,074 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:06,077 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:06,077 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:12,081 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:12,081 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:18,086 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:18,086 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:24,092 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:24,092 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:30,094 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:30,094 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:36,101 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:36,101 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:42,109 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:42,109 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:48,111 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:48,111 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:54,118 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:51:54,118 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:00,120 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:00,120 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:06,122 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:06,122 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:12,126 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:12,126 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:18,130 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:18,130 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:24,136 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:24,136 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:30,139 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:30,139 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:36,147 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:36,147 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:42,154 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:42,154 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:48,160 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:48,160 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:54,162 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:52:54,162 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:00,169 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:00,169 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:06,176 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:06,176 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:12,179 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:12,179 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:18,187 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:18,187 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:24,192 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:24,192 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:30,198 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:30,198 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:36,205 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:36,205 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:42,212 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:42,212 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:48,215 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:48,215 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:54,218 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:53:54,218 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:00,224 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:00,224 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:06,231 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:06,231 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:12,238 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:12,238 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:18,244 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:18,244 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:24,249 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:24,249 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:30,257 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:30,257 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:36,264 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:36,264 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:42,268 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:42,268 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:44,075 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:54:44,075 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:54:44,076 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: True, 任务ID: ab4a13df-9d21-45ad-b577-01356f28aa9d
2025-08-20 19:54:44,076 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: True, 任务ID: ab4a13df-9d21-45ad-b577-01356f28aa9d
2025-08-20 19:54:44,076 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: ab4a13df-9d21-45ad-b577-01356f28aa9d
2025-08-20 19:54:44,076 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: ab4a13df-9d21-45ad-b577-01356f28aa9d
2025-08-20 19:54:48,273 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:48,273 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:48,657 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 19:54:48,657 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 19:54:54,279 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:54,279 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:54:54,280 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:54:54,280 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:55:00,286 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:00,286 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:00,287 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:55:00,287 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:55:06,293 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:06,293 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:06,295 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:55:06,295 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:55:12,298 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:12,298 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:15,743 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:55:15,743 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:55:18,304 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:18,304 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:19,557 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:55:19,557 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:55:24,311 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:24,311 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:30,317 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:30,317 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:36,322 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:36,322 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:42,329 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:42,329 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:48,332 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:48,332 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:54,340 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:55:54,340 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:00,347 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:00,347 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:02,285 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:56:02,285 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:56:02,285 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: True, 任务ID: b296eeca-dcb5-4580-be12-951e9c4a6e91
2025-08-20 19:56:02,285 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: True, 任务ID: b296eeca-dcb5-4580-be12-951e9c4a6e91
2025-08-20 19:56:02,286 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: b296eeca-dcb5-4580-be12-951e9c4a6e91
2025-08-20 19:56:02,286 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: b296eeca-dcb5-4580-be12-951e9c4a6e91
2025-08-20 19:56:06,350 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:06,350 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:06,351 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:56:06,351 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:56:06,352 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 19:56:06,352 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 19:56:12,356 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:12,356 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:12,357 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:56:12,357 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:56:18,364 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:18,364 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:18,365 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:56:18,365 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:56:24,370 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:24,370 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:30,377 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:30,377 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:36,383 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:36,383 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:42,390 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:42,390 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:48,396 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:48,396 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:54,401 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:56:54,401 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:00,407 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:00,407 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:06,416 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:06,416 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:12,422 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:12,422 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:18,425 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:18,425 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:18,445 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:57:18,445 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:57:18,445 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: True, 任务ID: 6ed450fa-f23a-4bd6-9c40-2c396d0ab7bb
2025-08-20 19:57:18,445 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: True, 任务ID: 6ed450fa-f23a-4bd6-9c40-2c396d0ab7bb
2025-08-20 19:57:18,445 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 6ed450fa-f23a-4bd6-9c40-2c396d0ab7bb
2025-08-20 19:57:18,445 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 6ed450fa-f23a-4bd6-9c40-2c396d0ab7bb
2025-08-20 19:57:20,066 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:57:20,066 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:57:20,067 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: True, 任务ID: 7fa53a66-02aa-4a2f-941d-df156728c5fa
2025-08-20 19:57:20,067 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: True, 任务ID: 7fa53a66-02aa-4a2f-941d-df156728c5fa
2025-08-20 19:57:20,067 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 7fa53a66-02aa-4a2f-941d-df156728c5fa
2025-08-20 19:57:20,067 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 7fa53a66-02aa-4a2f-941d-df156728c5fa
2025-08-20 19:57:21,025 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 19:57:21,025 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 19:57:21,726 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 19:57:21,726 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 19:57:24,426 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:24,426 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:24,427 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:57:24,427 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:57:24,877 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:57:24,877 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 19:57:24,877 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: True, 任务ID: fc47ac67-7fae-4cfe-8c6a-87ce4d4d67cf
2025-08-20 19:57:24,877 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: True, 任务ID: fc47ac67-7fae-4cfe-8c6a-87ce4d4d67cf
2025-08-20 19:57:24,877 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: fc47ac67-7fae-4cfe-8c6a-87ce4d4d67cf
2025-08-20 19:57:24,877 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: fc47ac67-7fae-4cfe-8c6a-87ce4d4d67cf
2025-08-20 19:57:25,506 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 19:57:25,506 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 19:57:30,433 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:30,433 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:30,436 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:57:30,436 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:57:36,442 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:36,442 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:36,442 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:57:36,442 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:57:42,447 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:42,447 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:42,447 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:57:42,447 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:57:48,449 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:48,449 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:48,450 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:57:48,450 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:57:54,457 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:54,457 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:57:54,458 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:57:54,458 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:58:00,462 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:00,462 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:00,462 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:58:00,462 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 19:58:06,467 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:06,467 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:12,470 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:12,470 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:18,475 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:18,475 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:24,481 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:24,481 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:30,488 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:30,488 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:36,496 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:36,496 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:42,499 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:42,499 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:48,502 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:48,502 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:54,507 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:58:54,507 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:00,514 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:00,514 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:06,519 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:06,519 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:12,525 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:12,525 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:18,532 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:18,532 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:24,538 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:24,538 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:30,542 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:30,542 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:36,548 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:36,548 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:42,555 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:42,555 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:48,562 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:48,562 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:54,568 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 19:59:54,568 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:00,572 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:00,572 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:06,577 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:06,577 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:12,579 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:12,579 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:18,584 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:18,584 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:24,591 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:24,591 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:30,598 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:30,598 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:36,600 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:36,600 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:42,606 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:42,606 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:48,612 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:48,612 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:54,620 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:00:54,620 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:00,628 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:00,628 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:06,633 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:06,633 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:12,639 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:12,639 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:18,644 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:18,644 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:24,652 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:24,652 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:30,657 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:30,657 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:36,664 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:36,664 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:42,671 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:42,671 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:48,675 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:48,675 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:54,682 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:01:54,682 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:00,684 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:00,684 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:06,688 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:06,688 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:07,804 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 20:02:07,804 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 20:02:07,804 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 20:02:07,804 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 20:02:08,269 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:02:08,269 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:02:08,283 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:02:08,283 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:02:08,283 - app - INFO - 数据库初始化完成
2025-08-20 20:02:08,283 - app - INFO - 数据库初始化完成
2025-08-20 20:02:08,305 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:02:08,305 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:02:08,306 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:02:08,306 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:02:08,306 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:02:08,306 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:02:08,306 - app - INFO - 内存监控服务已启动
2025-08-20 20:02:08,306 - app - INFO - 内存监控服务已启动
2025-08-20 20:02:08,306 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:02:08,306 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:02:08,306 - app - INFO - 任务队列服务已启动
2025-08-20 20:02:08,306 - app - INFO - 任务队列服务已启动
2025-08-20 20:02:08,306 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:02:08,306 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:02:08,306 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:02:08,306 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:02:08,306 - app - INFO - 会话管理器已启动
2025-08-20 20:02:08,306 - app - INFO - 会话管理器已启动
2025-08-20 20:02:08,306 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:02:08,306 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:02:09,309 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:09,309 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:09,312 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 20:02:09,312 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 20:02:09,312 - app.services.session_manager - INFO - 会话管理器已停止
2025-08-20 20:02:09,312 - app.services.session_manager - INFO - 会话管理器已停止
2025-08-20 20:02:09,312 - app.services.task_queue - INFO - 任务队列处理器已停止
2025-08-20 20:02:09,312 - app.services.task_queue - INFO - 任务队列处理器已停止
2025-08-20 20:02:09,312 - app.services.memory_monitor - INFO - 内存监控服务已停止
2025-08-20 20:02:09,312 - app.services.memory_monitor - INFO - 内存监控服务已停止
2025-08-20 20:02:09,313 - app.models.database - INFO - 数据库连接已关闭
2025-08-20 20:02:09,313 - app.models.database - INFO - 数据库连接已关闭
2025-08-20 20:02:09,313 - app - INFO - Overall API Server 已关闭
2025-08-20 20:02:09,313 - app - INFO - Overall API Server 已关闭
2025-08-20 20:02:12,692 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:12,692 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:18,698 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:18,698 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:24,701 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:24,701 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:28,690 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 20:02:28,690 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 20:02:28,690 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 20:02:28,690 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 20:02:29,024 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:02:29,024 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:02:29,032 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:02:29,032 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:02:29,032 - app - INFO - 数据库初始化完成
2025-08-20 20:02:29,032 - app - INFO - 数据库初始化完成
2025-08-20 20:02:29,046 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:02:29,046 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:02:29,046 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:02:29,046 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:02:29,046 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:02:29,046 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:02:29,046 - app - INFO - 内存监控服务已启动
2025-08-20 20:02:29,046 - app - INFO - 内存监控服务已启动
2025-08-20 20:02:29,047 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:02:29,047 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:02:29,047 - app - INFO - 任务队列服务已启动
2025-08-20 20:02:29,047 - app - INFO - 任务队列服务已启动
2025-08-20 20:02:29,047 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:02:29,047 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:02:29,047 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:02:29,047 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:02:29,047 - app - INFO - 会话管理器已启动
2025-08-20 20:02:29,047 - app - INFO - 会话管理器已启动
2025-08-20 20:02:29,047 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:02:29,047 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:02:30,052 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:30,052 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:36,056 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:36,056 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:39,009 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:02:39,009 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:02:42,064 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:42,064 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:45,259 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:02:45,259 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:02:45,260 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: True, 任务ID: 14f67078-de49-4915-a044-e2acb946382e
2025-08-20 20:02:45,260 - app.api.ollama - INFO - 接收generate请求 - 模型: deepseek-r1:32b, 流式: True, 任务ID: 14f67078-de49-4915-a044-e2acb946382e
2025-08-20 20:02:45,260 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 14f67078-de49-4915-a044-e2acb946382e, 会话ID: None
2025-08-20 20:02:45,260 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 14f67078-de49-4915-a044-e2acb946382e, 会话ID: None
2025-08-20 20:02:46,586 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:02:46,586 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:02:48,068 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:48,068 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:54,073 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:02:54,073 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:00,080 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:00,080 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:06,087 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:06,087 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:06,087 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 20:03:06,087 - app.services.memory_monitor - WARNING - 内存使用率超过阈值: 0.9% >= 90.0%
2025-08-20 20:03:12,090 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:12,090 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:18,096 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:18,096 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:24,098 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:24,098 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:30,105 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:30,105 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:36,111 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:36,111 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:42,117 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:42,117 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:48,121 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:48,121 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:54,126 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:03:54,126 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:00,133 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:00,133 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:06,140 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:06,140 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:12,143 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:12,143 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:18,150 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:18,150 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:24,156 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:24,156 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:30,163 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:30,163 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:36,168 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:36,168 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:42,176 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:42,176 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:48,182 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:48,182 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:54,189 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:04:54,189 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:00,193 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:00,193 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:06,201 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:06,201 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:12,207 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:12,207 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:18,211 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:18,211 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:24,216 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:24,216 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:30,219 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:30,219 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:36,226 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:36,226 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:42,233 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:42,233 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:48,239 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:48,239 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:54,243 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:05:54,243 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:00,250 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:00,250 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:06,258 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:06,258 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:12,263 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:12,263 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:18,270 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:18,270 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:24,274 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:24,274 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:30,280 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:30,280 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:36,287 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:36,287 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:42,293 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:42,293 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:46,848 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:06:46,848 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:06:48,301 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:48,301 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:54,306 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:06:54,306 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:00,313 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:00,313 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:06,318 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:06,318 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:12,323 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:12,323 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:18,329 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:18,329 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:24,336 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:24,336 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:30,343 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:30,343 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:36,350 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:36,350 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:42,355 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:42,355 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:48,358 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:48,358 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:54,363 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:07:54,363 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:00,370 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:00,370 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:06,377 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:06,377 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:12,383 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:12,383 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:18,390 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:18,390 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:24,396 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:24,396 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:30,402 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:30,402 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:36,404 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:36,404 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:42,408 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:42,408 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:48,411 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:48,411 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:54,419 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:08:54,419 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:00,423 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:00,423 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:06,429 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:06,429 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:12,436 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:12,436 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:18,442 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:18,442 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:24,443 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:24,443 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:30,448 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:30,448 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:36,452 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:36,452 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:42,458 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:42,458 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:48,465 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:48,465 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:54,471 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:09:54,471 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:00,477 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:00,477 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:06,484 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:06,484 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:12,491 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:12,491 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:18,497 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:18,497 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:24,503 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:24,503 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:30,507 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:30,507 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:36,514 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:36,514 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:41,827 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 20:10:41,827 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 20:10:41,827 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 20:10:41,827 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 20:10:42,284 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:10:42,284 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:10:42,295 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:10:42,295 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:10:42,295 - app - INFO - 数据库初始化完成
2025-08-20 20:10:42,295 - app - INFO - 数据库初始化完成
2025-08-20 20:10:42,311 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:10:42,311 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:10:42,311 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:10:42,311 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:10:42,311 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:10:42,311 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:10:42,311 - app - INFO - 内存监控服务已启动
2025-08-20 20:10:42,311 - app - INFO - 内存监控服务已启动
2025-08-20 20:10:42,311 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:10:42,311 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:10:42,311 - app - INFO - 任务队列服务已启动
2025-08-20 20:10:42,311 - app - INFO - 任务队列服务已启动
2025-08-20 20:10:42,311 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:10:42,311 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:10:42,311 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:10:42,311 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:10:42,311 - app - INFO - 会话管理器已启动
2025-08-20 20:10:42,311 - app - INFO - 会话管理器已启动
2025-08-20 20:10:42,312 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:10:42,312 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:10:42,516 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:42,516 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:43,315 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:43,315 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:43,317 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 20:10:43,317 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 20:10:43,318 - app.services.session_manager - INFO - 会话管理器已停止
2025-08-20 20:10:43,318 - app.services.session_manager - INFO - 会话管理器已停止
2025-08-20 20:10:43,318 - app.services.task_queue - INFO - 任务队列处理器已停止
2025-08-20 20:10:43,318 - app.services.task_queue - INFO - 任务队列处理器已停止
2025-08-20 20:10:43,318 - app.services.memory_monitor - INFO - 内存监控服务已停止
2025-08-20 20:10:43,318 - app.services.memory_monitor - INFO - 内存监控服务已停止
2025-08-20 20:10:43,318 - app.models.database - INFO - 数据库连接已关闭
2025-08-20 20:10:43,318 - app.models.database - INFO - 数据库连接已关闭
2025-08-20 20:10:43,318 - app - INFO - Overall API Server 已关闭
2025-08-20 20:10:43,318 - app - INFO - Overall API Server 已关闭
2025-08-20 20:10:48,521 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:48,521 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:54,669 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 20:10:54,669 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 20:10:54,669 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 20:10:54,669 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 20:10:54,992 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:10:54,992 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:10:54,999 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:10:54,999 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:10:54,999 - app - INFO - 数据库初始化完成
2025-08-20 20:10:54,999 - app - INFO - 数据库初始化完成
2025-08-20 20:10:55,010 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:10:55,010 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:10:55,011 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:10:55,011 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:10:55,011 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:10:55,011 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:10:55,011 - app - INFO - 内存监控服务已启动
2025-08-20 20:10:55,011 - app - INFO - 内存监控服务已启动
2025-08-20 20:10:55,011 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:10:55,011 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:10:55,011 - app - INFO - 任务队列服务已启动
2025-08-20 20:10:55,011 - app - INFO - 任务队列服务已启动
2025-08-20 20:10:55,011 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:10:55,011 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:10:55,011 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:10:55,011 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:10:55,011 - app - INFO - 会话管理器已启动
2025-08-20 20:10:55,011 - app - INFO - 会话管理器已启动
2025-08-20 20:10:55,011 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:10:55,011 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:10:56,015 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:10:56,015 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:02,021 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:02,021 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:05,217 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:11:05,217 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:11:08,026 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:08,026 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:14,034 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:14,034 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:20,041 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:20,041 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:26,043 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:26,043 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:32,050 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:32,050 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:38,055 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:38,055 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:44,061 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:44,061 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:50,067 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:50,067 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:56,071 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:11:56,071 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:02,074 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:02,074 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:08,081 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:08,081 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:14,085 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:14,085 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:20,091 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:20,091 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:26,097 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:26,097 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:32,103 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:32,103 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:38,108 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:38,108 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:44,113 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:44,113 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:50,120 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:50,120 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:56,126 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:12:56,126 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:02,134 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:02,134 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:08,141 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:08,141 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:14,144 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:14,144 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:20,151 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:20,151 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:26,157 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:26,157 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:32,162 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:32,162 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:38,167 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:38,167 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:44,170 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:44,170 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:50,175 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:50,175 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:56,183 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:13:56,183 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:02,186 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:02,186 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:08,192 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:08,192 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:14,197 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:14,197 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:20,203 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:20,203 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:26,209 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:26,209 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:32,216 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:32,216 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:38,223 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:38,223 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:44,228 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:44,228 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:50,234 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:50,234 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:56,241 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:14:56,241 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:02,245 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:02,245 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:08,252 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:08,252 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:14,258 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:14,258 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:20,264 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:20,264 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:26,271 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:26,271 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:32,278 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:32,278 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:38,285 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:38,285 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:44,292 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:44,292 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:50,298 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:50,298 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:56,300 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:15:56,300 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:02,308 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:02,308 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:08,315 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:08,315 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:14,317 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:14,317 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:20,323 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:20,323 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:26,330 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:26,330 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:32,335 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:32,335 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:38,340 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:38,340 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:44,343 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:44,343 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:50,348 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:50,348 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:56,354 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:16:56,354 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:02,357 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:02,357 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:08,364 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:08,364 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:14,370 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:14,370 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:20,376 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:20,376 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:26,382 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:26,382 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:32,390 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:32,390 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:38,393 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:38,393 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:44,399 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:44,399 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:50,404 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:50,404 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:56,411 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:17:56,411 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:02,414 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:02,414 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:08,418 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:08,418 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:14,422 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:14,422 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:20,425 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:20,425 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:26,432 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:26,432 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:32,435 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:32,435 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:38,441 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:38,441 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:44,447 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:44,447 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:50,451 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:50,451 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:56,453 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:18:56,453 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:02,457 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:02,457 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:08,463 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:08,463 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:14,463 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:14,463 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:20,470 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:20,470 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:26,476 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:26,476 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:32,480 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:32,480 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:38,487 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:38,487 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:44,494 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:44,494 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:50,498 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:50,498 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:56,506 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:19:56,506 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:02,511 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:02,511 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:08,514 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:08,514 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:14,520 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:14,520 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:20,528 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:20,528 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:26,535 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:26,535 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:32,543 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:32,543 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:38,548 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:38,548 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:44,554 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:44,554 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:50,558 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:50,558 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:56,563 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:20:56,563 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:02,568 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:02,568 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:08,574 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:08,574 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:14,580 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:14,580 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:20,587 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:20,587 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:26,591 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:26,591 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:32,597 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:32,597 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:38,603 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:38,603 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:44,609 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:44,609 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:50,616 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:50,616 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:56,620 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:21:56,620 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:02,627 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:02,627 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:08,633 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:08,633 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:14,639 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:14,639 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:20,645 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:20,645 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:26,650 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:26,650 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:32,656 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:32,656 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:38,664 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:38,664 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:44,669 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:44,669 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:50,675 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:50,675 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:56,682 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:22:56,682 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:02,688 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:02,688 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:08,690 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:08,690 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:14,696 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:14,696 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:20,700 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:20,700 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:26,704 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:26,704 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:32,710 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:32,710 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:38,713 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:38,713 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:44,721 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:44,721 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:50,728 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:50,728 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:56,734 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:23:56,734 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:02,739 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:02,739 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:08,746 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:08,746 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:14,752 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:14,752 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:20,759 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:20,759 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:26,766 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:26,766 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:32,772 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:32,772 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:38,779 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:38,779 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:44,787 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:44,787 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:50,793 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:50,793 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:56,800 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:24:56,800 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:02,803 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:02,803 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:08,809 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:08,809 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:14,817 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:14,817 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:20,823 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:20,823 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:26,829 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:26,829 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:32,837 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:32,837 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:38,843 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:38,843 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:44,848 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:44,848 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:50,853 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:50,853 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:56,860 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:25:56,860 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:02,867 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:02,867 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:08,874 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:08,874 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:14,881 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:14,881 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:20,884 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:20,884 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:26,888 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:26,888 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:32,894 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:32,894 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:38,898 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:38,898 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:44,904 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:44,904 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:50,912 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:50,912 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:56,918 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:26:56,918 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:02,922 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:02,922 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:08,929 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:08,929 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:14,935 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:14,935 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:20,943 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:20,943 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:26,951 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:26,951 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:32,958 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:32,958 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:38,965 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:38,965 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:44,971 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:44,971 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:50,975 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:50,975 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:56,980 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:27:56,980 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:02,987 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:02,987 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:08,992 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:08,992 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:14,996 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:14,996 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:21,002 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:21,002 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:27,007 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:27,007 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:33,014 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:33,014 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:39,021 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:39,021 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:45,028 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:45,028 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:51,033 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:51,033 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:57,039 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:28:57,039 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:03,045 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:03,045 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:09,047 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:09,047 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:15,050 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:15,050 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:21,057 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:21,057 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:27,061 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:27,061 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:33,064 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:33,064 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:39,070 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:39,070 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:45,077 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:45,077 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:51,081 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:51,081 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:57,085 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:29:57,085 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:03,091 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:03,091 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:09,094 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:09,094 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:15,100 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:15,100 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:21,107 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:21,107 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:27,111 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:27,111 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:33,118 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:33,118 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:39,125 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:39,125 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:45,131 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:45,131 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:51,138 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:51,138 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:57,145 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:30:57,145 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:03,151 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:03,151 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:09,157 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:09,157 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:15,165 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:15,165 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:21,172 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:21,172 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:27,178 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:27,178 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:33,185 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:33,185 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:39,190 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:39,190 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:45,195 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:45,195 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:51,201 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:51,201 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:57,206 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:31:57,206 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:03,211 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:03,211 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:09,219 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:09,219 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:15,224 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:15,224 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:21,230 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:21,230 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:27,236 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:27,236 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:33,243 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:33,243 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:39,250 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:39,250 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:45,253 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:45,253 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:51,258 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:51,258 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:57,262 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:32:57,262 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:03,268 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:03,268 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:09,275 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:09,275 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:15,282 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:15,282 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:21,289 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:21,289 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:27,296 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:27,296 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:33,303 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:33,303 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:39,309 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:39,309 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:45,316 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:45,316 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:51,324 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:51,324 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:57,330 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:33:57,330 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:03,336 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:03,336 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:09,342 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:09,342 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:15,347 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:15,347 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:21,349 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:21,349 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:27,356 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:27,356 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:33,358 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:33,358 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:39,365 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:39,365 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:45,370 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:45,370 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:51,374 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:51,374 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:57,381 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:34:57,381 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:03,387 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:03,387 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:09,391 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:09,391 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:15,396 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:15,396 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:21,401 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:21,401 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:27,181 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 20:35:27,182 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 20:35:27,182 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 20:35:27,182 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 20:35:27,407 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:27,407 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:27,506 - app.services.test_plan_generator - INFO - prompt模板加载成功
2025-08-20 20:35:27,506 - app.services.test_plan_generator - INFO - 测试计划生成器初始化成功，默认模型: deepseek-r1:32b
2025-08-20 20:35:27,528 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:35:27,528 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:35:27,539 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:35:27,539 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:35:27,539 - app - INFO - 数据库初始化完成
2025-08-20 20:35:27,539 - app - INFO - 数据库初始化完成
2025-08-20 20:35:27,559 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:35:27,559 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:35:27,559 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:35:27,559 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:35:27,559 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:35:27,559 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:35:27,559 - app - INFO - 内存监控服务已启动
2025-08-20 20:35:27,559 - app - INFO - 内存监控服务已启动
2025-08-20 20:35:27,559 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:35:27,559 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:35:27,560 - app - INFO - 任务队列服务已启动
2025-08-20 20:35:27,560 - app - INFO - 任务队列服务已启动
2025-08-20 20:35:27,560 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:35:27,560 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:35:27,560 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:35:27,560 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:35:27,560 - app - INFO - 会话管理器已启动
2025-08-20 20:35:27,560 - app - INFO - 会话管理器已启动
2025-08-20 20:35:27,560 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:35:27,560 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:35:28,564 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:28,564 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:28,565 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 20:35:28,565 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 20:35:28,565 - app.services.session_manager - INFO - 会话管理器已停止
2025-08-20 20:35:28,565 - app.services.session_manager - INFO - 会话管理器已停止
2025-08-20 20:35:28,565 - app.services.task_queue - INFO - 任务队列处理器已停止
2025-08-20 20:35:28,565 - app.services.task_queue - INFO - 任务队列处理器已停止
2025-08-20 20:35:28,565 - app.services.memory_monitor - INFO - 内存监控服务已停止
2025-08-20 20:35:28,565 - app.services.memory_monitor - INFO - 内存监控服务已停止
2025-08-20 20:35:28,565 - app.models.database - INFO - 数据库连接已关闭
2025-08-20 20:35:28,565 - app.models.database - INFO - 数据库连接已关闭
2025-08-20 20:35:28,566 - app - INFO - Overall API Server 已关闭
2025-08-20 20:35:28,566 - app - INFO - Overall API Server 已关闭
2025-08-20 20:35:33,412 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:33,412 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:39,420 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:39,420 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:43,527 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 20:35:43,527 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 20:35:43,527 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 20:35:43,527 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 20:35:43,848 - app.services.test_plan_generator - INFO - prompt模板加载成功
2025-08-20 20:35:43,848 - app.services.test_plan_generator - INFO - 测试计划生成器初始化成功，默认模型: deepseek-r1:32b
2025-08-20 20:35:43,868 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:35:43,868 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:35:43,877 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:35:43,877 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:35:43,877 - app - INFO - 数据库初始化完成
2025-08-20 20:35:43,877 - app - INFO - 数据库初始化完成
2025-08-20 20:35:43,889 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:35:43,889 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:35:43,889 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:35:43,889 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:35:43,889 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:35:43,889 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:35:43,890 - app - INFO - 内存监控服务已启动
2025-08-20 20:35:43,890 - app - INFO - 内存监控服务已启动
2025-08-20 20:35:43,890 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:35:43,890 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:35:43,890 - app - INFO - 任务队列服务已启动
2025-08-20 20:35:43,890 - app - INFO - 任务队列服务已启动
2025-08-20 20:35:43,890 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:35:43,890 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:35:43,890 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:35:43,890 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:35:43,890 - app - INFO - 会话管理器已启动
2025-08-20 20:35:43,890 - app - INFO - 会话管理器已启动
2025-08-20 20:35:43,890 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:35:43,890 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:35:44,896 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:44,896 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:48,739 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 0aa27145-38c9-443b-8afa-48eae8bb160b
2025-08-20 20:35:48,739 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 0aa27145-38c9-443b-8afa-48eae8bb160b
2025-08-20 20:35:48,740 - app.services.test_plan_generator - INFO - 开始生成测试计划: 在iOS设备上打开美团首页，点击搜索框，输入火锅，然后截图...
2025-08-20 20:35:48,740 - app.services.test_plan_generator - INFO - 开始生成测试计划: 在iOS设备上打开美团首页，点击搜索框，输入火锅，然后截图...
2025-08-20 20:35:48,740 - app.services.test_plan_generator - INFO - 从文本中检测到平台: IOS
2025-08-20 20:35:48,740 - app.services.test_plan_generator - INFO - 从文本中检测到平台: IOS
2025-08-20 20:35:48,765 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:35:48,765 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:35:48,766 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:35:48,766 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:35:48,767 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: b0d6a402-4a86-4255-a2a5-c703a15007be, 会话ID: None
2025-08-20 20:35:48,767 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: b0d6a402-4a86-4255-a2a5-c703a15007be, 会话ID: None
2025-08-20 20:35:50,904 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:50,904 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:56,911 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:35:56,911 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:36:02,916 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:36:02,916 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:36:08,922 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:36:08,922 - app.services.memory_monitor - ERROR - 保存内存统计失败: 'NoneType' object is not callable
2025-08-20 20:36:12,560 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 20:36:12,560 - app - INFO - 正在关闭 Overall API Server...
2025-08-20 20:36:12,560 - app.services.session_manager - INFO - 会话管理器已停止
2025-08-20 20:36:12,560 - app.services.session_manager - INFO - 会话管理器已停止
2025-08-20 20:36:12,560 - app.services.task_queue - INFO - 任务队列处理器已停止
2025-08-20 20:36:12,560 - app.services.task_queue - INFO - 任务队列处理器已停止
2025-08-20 20:36:12,560 - app.services.memory_monitor - INFO - 内存监控服务已停止
2025-08-20 20:36:12,560 - app.services.memory_monitor - INFO - 内存监控服务已停止
2025-08-20 20:36:12,561 - app.models.database - INFO - 数据库连接已关闭
2025-08-20 20:36:12,561 - app.models.database - INFO - 数据库连接已关闭
2025-08-20 20:36:12,562 - app - INFO - Overall API Server 已关闭
2025-08-20 20:36:12,562 - app - INFO - Overall API Server 已关闭
2025-08-20 20:42:25,642 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 20:42:25,642 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 20:42:25,642 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 20:42:25,642 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 20:42:26,128 - app.services.test_plan_generator - INFO - 尝试加载prompt模板: /Users/<USER>/Desktop/before_work/local_overall_api/app/data/prompts/test_plan_prompts.json
2025-08-20 20:42:26,128 - app.services.test_plan_generator - INFO - prompt模板加载成功
2025-08-20 20:42:26,128 - app.services.test_plan_generator - INFO - 测试计划生成器初始化成功，默认模型: deepseek-r1:32b
2025-08-20 20:42:26,153 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:42:26,153 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:42:26,164 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:42:26,164 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:42:26,164 - app - INFO - 数据库初始化完成
2025-08-20 20:42:26,164 - app - INFO - 数据库初始化完成
2025-08-20 20:42:26,190 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:42:26,190 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:42:26,190 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:42:26,190 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:42:26,190 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:42:26,190 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:42:26,190 - app - INFO - 内存监控服务已启动
2025-08-20 20:42:26,190 - app - INFO - 内存监控服务已启动
2025-08-20 20:42:26,191 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:42:26,191 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:42:26,191 - app - INFO - 任务队列服务已启动
2025-08-20 20:42:26,191 - app - INFO - 任务队列服务已启动
2025-08-20 20:42:26,191 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:42:26,191 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:42:26,191 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:42:26,191 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:42:26,191 - app - INFO - 会话管理器已启动
2025-08-20 20:42:26,191 - app - INFO - 会话管理器已启动
2025-08-20 20:42:26,191 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:42:26,191 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:42:36,894 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:42:36,894 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:42:43,385 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 4521ffdf-21e7-410c-85a5-d51b518876e9
2025-08-20 20:42:43,385 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 4521ffdf-21e7-410c-85a5-d51b518876e9
2025-08-20 20:42:43,385 - app.services.test_plan_generator - INFO - 开始生成测试计划: 在iOS设备上打开美团首页，点击搜索框，输入火锅，然后截图...
2025-08-20 20:42:43,385 - app.services.test_plan_generator - INFO - 开始生成测试计划: 在iOS设备上打开美团首页，点击搜索框，输入火锅，然后截图...
2025-08-20 20:42:43,385 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:42:43,385 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:42:43,394 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:42:43,394 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:42:43,394 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:42:43,394 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:42:43,394 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 6b9a9d11-cf99-41c7-9fd4-46aa48b9c939, 会话ID: None
2025-08-20 20:42:43,394 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 6b9a9d11-cf99-41c7-9fd4-46aa48b9c939, 会话ID: None
2025-08-20 20:43:13,395 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 0c53200a-20fe-49af-940f-c9d6b0d7bfa5
2025-08-20 20:43:13,395 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 0c53200a-20fe-49af-940f-c9d6b0d7bfa5
2025-08-20 20:43:13,395 - app.services.test_plan_generator - INFO - 开始生成测试计划: 打开淘宝应用，搜索手机，查看第一个商品详情...
2025-08-20 20:43:13,395 - app.services.test_plan_generator - INFO - 开始生成测试计划: 打开淘宝应用，搜索手机，查看第一个商品详情...
2025-08-20 20:43:13,396 - app.services.test_plan_generator - INFO - 轮询选择平台: IOS
2025-08-20 20:43:13,396 - app.services.test_plan_generator - INFO - 轮询选择平台: IOS
2025-08-20 20:43:13,405 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:43:13,405 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:43:13,406 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:43:13,406 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:43:13,406 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 0ce3b73f-3db9-4826-8b41-a7bec69a1da1, 会话ID: None
2025-08-20 20:43:13,406 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 0ce3b73f-3db9-4826-8b41-a7bec69a1da1, 会话ID: None
2025-08-20 20:44:28,310 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 7ab1ea0a-9daa-4bc7-af13-1ec1d7f0f5a8
2025-08-20 20:44:28,310 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 7ab1ea0a-9daa-4bc7-af13-1ec1d7f0f5a8
2025-08-20 20:44:28,311 - app.services.test_plan_generator - INFO - 开始生成测试计划: 点击按钮...
2025-08-20 20:44:28,311 - app.services.test_plan_generator - INFO - 开始生成测试计划: 点击按钮...
2025-08-20 20:44:28,311 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:44:28,311 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:44:28,334 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:44:28,334 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:44:28,334 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:44:28,334 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:44:28,334 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 4c4cf756-dc7d-4dfe-bcb0-8ed52baf8c98, 会话ID: None
2025-08-20 20:44:28,334 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 4c4cf756-dc7d-4dfe-bcb0-8ed52baf8c98, 会话ID: None
2025-08-20 20:47:43,400 - app.services.ollama_proxy - ERROR - 非流式generate请求失败: 
2025-08-20 20:47:43,400 - app.services.ollama_proxy - ERROR - 非流式generate请求失败: 
2025-08-20 20:47:43,402 - app.services.test_plan_generator - ERROR - 生成计划时发生异常: 
2025-08-20 20:47:43,402 - app.services.test_plan_generator - ERROR - 生成计划时发生异常: 
2025-08-20 20:47:43,408 - app.api.test_plan - ERROR - 测试计划生成失败 - 记录ID: 4521ffdf-21e7-410c-85a5-d51b518876e9, 错误: 生成计划时发生异常: 
2025-08-20 20:47:43,408 - app.api.test_plan - ERROR - 测试计划生成失败 - 记录ID: 4521ffdf-21e7-410c-85a5-d51b518876e9, 错误: 生成计划时发生异常: 
2025-08-20 20:48:13,411 - app.services.ollama_proxy - ERROR - 非流式generate请求失败: 
2025-08-20 20:48:13,411 - app.services.ollama_proxy - ERROR - 非流式generate请求失败: 
2025-08-20 20:48:13,412 - app.services.test_plan_generator - ERROR - 生成计划时发生异常: 
2025-08-20 20:48:13,412 - app.services.test_plan_generator - ERROR - 生成计划时发生异常: 
2025-08-20 20:48:13,415 - app.api.test_plan - ERROR - 测试计划生成失败 - 记录ID: 0c53200a-20fe-49af-940f-c9d6b0d7bfa5, 错误: 生成计划时发生异常: 
2025-08-20 20:48:13,415 - app.api.test_plan - ERROR - 测试计划生成失败 - 记录ID: 0c53200a-20fe-49af-940f-c9d6b0d7bfa5, 错误: 生成计划时发生异常: 
2025-08-20 20:48:15,478 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:48:15,478 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:48:15,480 - app.services.ollama_proxy - INFO - 非流式generate请求完成 - 任务ID: 4c4cf756-dc7d-4dfe-bcb0-8ed52baf8c98
2025-08-20 20:48:15,480 - app.services.ollama_proxy - INFO - 非流式generate请求完成 - 任务ID: 4c4cf756-dc7d-4dfe-bcb0-8ed52baf8c98
2025-08-20 20:48:15,480 - app.services.test_plan_generator - INFO - 模型调用完成，耗时: 227.17秒
2025-08-20 20:48:15,480 - app.services.test_plan_generator - INFO - 模型调用完成，耗时: 227.17秒
2025-08-20 20:48:15,480 - app.services.test_plan_generator - ERROR - 未找到有效的JSON格式
2025-08-20 20:48:15,480 - app.services.test_plan_generator - ERROR - 未找到有效的JSON格式
2025-08-20 20:48:15,484 - app.api.test_plan - ERROR - 测试计划生成失败 - 记录ID: 7ab1ea0a-9daa-4bc7-af13-1ec1d7f0f5a8, 错误: 未找到有效的JSON格式
2025-08-20 20:48:15,484 - app.api.test_plan - ERROR - 测试计划生成失败 - 记录ID: 7ab1ea0a-9daa-4bc7-af13-1ec1d7f0f5a8, 错误: 未找到有效的JSON格式
2025-08-20 20:48:46,695 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-20 20:48:46,695 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-20 20:48:46,695 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-20 20:48:46,695 - __main__ - INFO - 内存阈值: 90.0%
2025-08-20 20:48:47,211 - app.services.test_plan_generator - INFO - 尝试加载prompt模板: /Users/<USER>/Desktop/before_work/local_overall_api/app/data/prompts/test_plan_prompts.json
2025-08-20 20:48:47,211 - app.services.test_plan_generator - INFO - prompt模板加载成功
2025-08-20 20:48:47,211 - app.services.test_plan_generator - INFO - 测试计划生成器初始化成功，默认模型: deepseek-r1:32b
2025-08-20 20:48:47,235 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:48:47,235 - app - INFO - 正在启动 Overall API Server...
2025-08-20 20:48:47,246 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:48:47,246 - app.models.database - INFO - 数据库初始化成功
2025-08-20 20:48:47,246 - app - INFO - 数据库初始化完成
2025-08-20 20:48:47,246 - app - INFO - 数据库初始化完成
2025-08-20 20:48:47,270 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:48:47,270 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:48:47,270 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:48:47,270 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-20 20:48:47,270 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:48:47,270 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-20 20:48:47,270 - app - INFO - 内存监控服务已启动
2025-08-20 20:48:47,270 - app - INFO - 内存监控服务已启动
2025-08-20 20:48:47,270 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:48:47,270 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-20 20:48:47,270 - app - INFO - 任务队列服务已启动
2025-08-20 20:48:47,270 - app - INFO - 任务队列服务已启动
2025-08-20 20:48:47,270 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:48:47,270 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-20 20:48:47,270 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:48:47,270 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-20 20:48:47,270 - app - INFO - 会话管理器已启动
2025-08-20 20:48:47,270 - app - INFO - 会话管理器已启动
2025-08-20 20:48:47,271 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:48:47,271 - app - INFO - Overall API Server 启动完成!
2025-08-20 20:49:03,224 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 5ba45d10-52d0-4f31-8663-b3e11f02d74d
2025-08-20 20:49:03,224 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 5ba45d10-52d0-4f31-8663-b3e11f02d74d
2025-08-20 20:49:03,224 - app.services.test_plan_generator - INFO - 开始生成测试计划: 点击按钮...
2025-08-20 20:49:03,224 - app.services.test_plan_generator - INFO - 开始生成测试计划: 点击按钮...
2025-08-20 20:49:03,224 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:49:03,224 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:49:03,232 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:49:03,232 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:49:03,233 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:49:03,233 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:49:03,233 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 6a226f1c-9b7a-4f6b-9ee2-356d9f7ab7b6, 会话ID: None
2025-08-20 20:49:03,233 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 6a226f1c-9b7a-4f6b-9ee2-356d9f7ab7b6, 会话ID: None
2025-08-20 20:49:04,247 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:49:04,247 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:50:41,343 - app.services.test_plan_generator - INFO - 流式生成完成，总长度: 3655
2025-08-20 20:50:41,343 - app.services.test_plan_generator - INFO - 流式生成完成，总长度: 3655
2025-08-20 20:52:53,366 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 17dae957-3fb5-43b3-8ddf-8d419ec9884b
2025-08-20 20:52:53,366 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 17dae957-3fb5-43b3-8ddf-8d419ec9884b
2025-08-20 20:52:53,367 - app.services.test_plan_generator - INFO - 开始生成测试计划: 点击按钮...
2025-08-20 20:52:53,367 - app.services.test_plan_generator - INFO - 开始生成测试计划: 点击按钮...
2025-08-20 20:52:53,367 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:52:53,367 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:52:53,391 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:52:53,391 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:52:53,392 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:52:53,392 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:52:53,392 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: a73a604e-5396-4b81-b42d-7ed41707bde7, 会话ID: None
2025-08-20 20:52:53,392 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: a73a604e-5396-4b81-b42d-7ed41707bde7, 会话ID: None
2025-08-20 20:54:34,141 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:54:34,141 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:54:34,145 - app.services.ollama_proxy - INFO - 非流式generate请求完成 - 任务ID: a73a604e-5396-4b81-b42d-7ed41707bde7
2025-08-20 20:54:34,145 - app.services.ollama_proxy - INFO - 非流式generate请求完成 - 任务ID: a73a604e-5396-4b81-b42d-7ed41707bde7
2025-08-20 20:54:34,145 - app.services.test_plan_generator - INFO - 模型调用完成，耗时: 100.78秒
2025-08-20 20:54:34,145 - app.services.test_plan_generator - INFO - 模型调用完成，耗时: 100.78秒
2025-08-20 20:54:34,145 - app.services.test_plan_generator - ERROR - 未找到有效的JSON格式
2025-08-20 20:54:34,145 - app.services.test_plan_generator - ERROR - 未找到有效的JSON格式
2025-08-20 20:54:34,157 - app.api.test_plan - ERROR - 测试计划生成失败 - 记录ID: 17dae957-3fb5-43b3-8ddf-8d419ec9884b, 错误: 未找到有效的JSON格式
2025-08-20 20:54:34,157 - app.api.test_plan - ERROR - 测试计划生成失败 - 记录ID: 17dae957-3fb5-43b3-8ddf-8d419ec9884b, 错误: 未找到有效的JSON格式
2025-08-20 20:54:35,170 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: e2e7598b-f60f-46bf-8b2f-888762f7d75f
2025-08-20 20:54:35,170 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: e2e7598b-f60f-46bf-8b2f-888762f7d75f
2025-08-20 20:54:35,171 - app.services.test_plan_generator - INFO - 开始生成测试计划: 打开淘宝应用，搜索手机，查看第一个商品详情...
2025-08-20 20:54:35,171 - app.services.test_plan_generator - INFO - 开始生成测试计划: 打开淘宝应用，搜索手机，查看第一个商品详情...
2025-08-20 20:54:35,171 - app.services.test_plan_generator - INFO - 使用指定平台: ANDROID
2025-08-20 20:54:35,171 - app.services.test_plan_generator - INFO - 使用指定平台: ANDROID
2025-08-20 20:54:35,192 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:54:35,192 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:54:35,193 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:54:35,193 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:54:35,193 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: c9e938a2-56fd-4588-9d48-cb5c854b2a85, 会话ID: None
2025-08-20 20:54:35,193 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: c9e938a2-56fd-4588-9d48-cb5c854b2a85, 会话ID: None
2025-08-20 20:54:36,821 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:54:36,821 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:55:16,137 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:55:16,137 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:55:32,472 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 285c341c-3af4-4719-b7bd-f783c1a45003
2025-08-20 20:55:32,472 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 285c341c-3af4-4719-b7bd-f783c1a45003
2025-08-20 20:55:32,472 - app.services.test_plan_generator - INFO - 开始生成测试计划: 点击首页的搜索按钮...
2025-08-20 20:55:32,472 - app.services.test_plan_generator - INFO - 开始生成测试计划: 点击首页的搜索按钮...
2025-08-20 20:55:32,472 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:55:32,472 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:55:32,482 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:55:32,482 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:55:32,482 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:55:32,482 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:55:32,482 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 8958ad30-e58a-49a5-82ad-38a579edce9a, 会话ID: None
2025-08-20 20:55:32,482 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 8958ad30-e58a-49a5-82ad-38a579edce9a, 会话ID: None
2025-08-20 20:56:44,869 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 7185a880-9e2d-4ec0-a8b8-f5849464be4a
2025-08-20 20:56:44,869 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 7185a880-9e2d-4ec0-a8b8-f5849464be4a
2025-08-20 20:56:44,869 - app.services.test_plan_generator - INFO - 开始生成测试计划: 点击按钮...
2025-08-20 20:56:44,869 - app.services.test_plan_generator - INFO - 开始生成测试计划: 点击按钮...
2025-08-20 20:56:44,869 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:56:44,869 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:56:44,888 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:56:44,888 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:56:44,888 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:56:44,888 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:56:44,888 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 62b65fc7-7723-415c-9f1f-f67f140ff2f4, 会话ID: None
2025-08-20 20:56:44,888 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 62b65fc7-7723-415c-9f1f-f67f140ff2f4, 会话ID: None
2025-08-20 20:56:45,446 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:56:45,446 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:57:27,160 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:57:27,160 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:57:27,162 - app.services.ollama_proxy - INFO - 非流式generate请求完成 - 任务ID: 8958ad30-e58a-49a5-82ad-38a579edce9a
2025-08-20 20:57:27,162 - app.services.ollama_proxy - INFO - 非流式generate请求完成 - 任务ID: 8958ad30-e58a-49a5-82ad-38a579edce9a
2025-08-20 20:57:27,162 - app.services.test_plan_generator - INFO - 模型调用完成，耗时: 114.69秒
2025-08-20 20:57:27,162 - app.services.test_plan_generator - INFO - 模型调用完成，耗时: 114.69秒
2025-08-20 20:57:27,162 - app.services.test_plan_generator - ERROR - 未找到有效的JSON格式
2025-08-20 20:57:27,162 - app.services.test_plan_generator - ERROR - 未找到有效的JSON格式
2025-08-20 20:57:27,167 - app.api.test_plan - ERROR - 测试计划生成失败 - 记录ID: 285c341c-3af4-4719-b7bd-f783c1a45003, 错误: 未找到有效的JSON格式
2025-08-20 20:57:27,167 - app.api.test_plan - ERROR - 测试计划生成失败 - 记录ID: 285c341c-3af4-4719-b7bd-f783c1a45003, 错误: 未找到有效的JSON格式
2025-08-20 20:57:43,149 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 629029e5-9896-4f81-b84b-40476aa5325c
2025-08-20 20:57:43,149 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 629029e5-9896-4f81-b84b-40476aa5325c
2025-08-20 20:57:43,149 - app.services.test_plan_generator - INFO - 开始生成测试计划: 点击按钮...
2025-08-20 20:57:43,149 - app.services.test_plan_generator - INFO - 开始生成测试计划: 点击按钮...
2025-08-20 20:57:43,149 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:57:43,149 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:57:43,169 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:57:43,169 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:57:43,170 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:57:43,170 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:57:43,170 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: a98a3775-f855-49dc-93ac-0f023a5d1e91, 会话ID: None
2025-08-20 20:57:43,170 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: a98a3775-f855-49dc-93ac-0f023a5d1e91, 会话ID: None
2025-08-20 20:57:44,264 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:57:44,264 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:58:19,264 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 48cd27dc-f9d4-45a1-bb65-688d0f312701
2025-08-20 20:58:19,264 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 48cd27dc-f9d4-45a1-bb65-688d0f312701
2025-08-20 20:58:19,265 - app.services.test_plan_generator - INFO - 开始生成测试计划: 点击按钮...
2025-08-20 20:58:19,265 - app.services.test_plan_generator - INFO - 开始生成测试计划: 点击按钮...
2025-08-20 20:58:19,265 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:58:19,265 - app.services.test_plan_generator - INFO - 使用指定平台: IOS
2025-08-20 20:58:19,291 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:58:19,291 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:58:19,291 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:58:19,291 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 20:58:19,291 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: d4989dd0-ca09-4b45-a5d6-303aa9901671, 会话ID: None
2025-08-20 20:58:19,291 - app.services.ollama_proxy - INFO - 开始非流式generate请求 - 模型: deepseek-r1:32b, 任务ID: d4989dd0-ca09-4b45-a5d6-303aa9901671, 会话ID: None
2025-08-20 20:59:12,801 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:59:12,801 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 20:59:53,795 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:59:53,795 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 20:59:53,797 - app.services.ollama_proxy - INFO - 非流式generate请求完成 - 任务ID: d4989dd0-ca09-4b45-a5d6-303aa9901671
2025-08-20 20:59:53,797 - app.services.ollama_proxy - INFO - 非流式generate请求完成 - 任务ID: d4989dd0-ca09-4b45-a5d6-303aa9901671
2025-08-20 20:59:53,798 - app.services.test_plan_generator - INFO - 模型调用完成，耗时: 94.53秒
2025-08-20 20:59:53,798 - app.services.test_plan_generator - INFO - 模型调用完成，耗时: 94.53秒
2025-08-20 20:59:53,798 - app.services.test_plan_generator - ERROR - 未找到有效的JSON格式
2025-08-20 20:59:53,798 - app.services.test_plan_generator - ERROR - 未找到有效的JSON格式
2025-08-20 20:59:54,812 - app.api.test_plan - ERROR - 测试计划生成失败 - 记录ID: 48cd27dc-f9d4-45a1-bb65-688d0f312701, 错误: 未找到有效的JSON格式
2025-08-20 20:59:54,812 - app.api.test_plan - ERROR - 测试计划生成失败 - 记录ID: 48cd27dc-f9d4-45a1-bb65-688d0f312701, 错误: 未找到有效的JSON格式
2025-08-20 21:00:18,840 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 21:00:18,840 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 21:01:00,863 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: e65bac87-e248-47bf-b83b-bc9fe95b23c1
2025-08-20 21:01:00,863 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: e65bac87-e248-47bf-b83b-bc9fe95b23c1
2025-08-20 21:01:00,864 - app.services.test_plan_generator - INFO - 开始生成测试计划: 打开美团外卖APP，搜索火锅，选择第一家店，加入购物车后下单...
2025-08-20 21:01:00,864 - app.services.test_plan_generator - INFO - 开始生成测试计划: 打开美团外卖APP，搜索火锅，选择第一家店，加入购物车后下单...
2025-08-20 21:01:00,864 - app.services.test_plan_generator - INFO - 使用指定平台: ANDROID
2025-08-20 21:01:00,864 - app.services.test_plan_generator - INFO - 使用指定平台: ANDROID
2025-08-20 21:01:00,886 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 21:01:00,886 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-20 21:01:00,887 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 21:01:00,887 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-20 21:01:00,887 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: e3dcdc39-2aea-4ffa-9378-bf895dc05bba, 会话ID: None
2025-08-20 21:01:00,887 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: e3dcdc39-2aea-4ffa-9378-bf895dc05bba, 会话ID: None
2025-08-20 21:01:02,321 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-20 21:01:02,321 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-21 08:15:20,334 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:15:20,334 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:15:50,427 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-21 08:15:50,427 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-21 08:15:50,427 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-21 08:15:50,427 - __main__ - INFO - 内存阈值: 90.0%
2025-08-21 08:15:50,863 - app.services.test_plan_generator - INFO - 尝试加载prompt模板: /Users/<USER>/Desktop/before_work/local_overall_api/app/data/prompts/test_plan_prompts.json
2025-08-21 08:15:50,863 - app.services.test_plan_generator - INFO - prompt模板加载成功
2025-08-21 08:15:50,864 - app.services.test_plan_generator - INFO - 测试计划生成器初始化成功，默认模型: deepseek-r1:32b
2025-08-21 08:15:50,900 - app - INFO - 正在启动 Overall API Server...
2025-08-21 08:15:50,900 - app - INFO - 正在启动 Overall API Server...
2025-08-21 08:15:50,911 - app.models.database - INFO - 数据库初始化成功
2025-08-21 08:15:50,911 - app.models.database - INFO - 数据库初始化成功
2025-08-21 08:15:50,911 - app - INFO - 数据库初始化完成
2025-08-21 08:15:50,911 - app - INFO - 数据库初始化完成
2025-08-21 08:15:50,929 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:15:50,929 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:15:50,930 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-21 08:15:50,930 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-21 08:15:50,930 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-21 08:15:50,930 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-21 08:15:50,930 - app - INFO - 内存监控服务已启动
2025-08-21 08:15:50,930 - app - INFO - 内存监控服务已启动
2025-08-21 08:15:50,930 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-21 08:15:50,930 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-21 08:15:50,930 - app - INFO - 任务队列服务已启动
2025-08-21 08:15:50,930 - app - INFO - 任务队列服务已启动
2025-08-21 08:15:50,930 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-21 08:15:50,930 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-21 08:15:50,930 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-21 08:15:50,930 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-21 08:15:50,930 - app - INFO - 会话管理器已启动
2025-08-21 08:15:50,930 - app - INFO - 会话管理器已启动
2025-08-21 08:15:50,930 - app - INFO - Overall API Server 启动完成!
2025-08-21 08:15:50,930 - app - INFO - Overall API Server 启动完成!
2025-08-21 08:16:06,067 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:16:06,067 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:33:49,993 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:33:49,993 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:34:00,704 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:34:00,704 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:34:11,531 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:34:11,531 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:34:20,969 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:34:20,969 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:34:27,099 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 7bc2521f-c583-490f-8767-e1168714f2bd
2025-08-21 08:34:27,099 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 7bc2521f-c583-490f-8767-e1168714f2bd
2025-08-21 08:34:27,100 - app.services.test_plan_generator - INFO - 开始生成测试计划: 在美团首页搜索iPhone15，查看第一个商品详情，截图保存后返回首页...
2025-08-21 08:34:27,100 - app.services.test_plan_generator - INFO - 开始生成测试计划: 在美团首页搜索iPhone15，查看第一个商品详情，截图保存后返回首页...
2025-08-21 08:34:27,100 - app.services.test_plan_generator - INFO - 从文本中检测到平台: IOS
2025-08-21 08:34:27,100 - app.services.test_plan_generator - INFO - 从文本中检测到平台: IOS
2025-08-21 08:34:27,111 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:34:27,111 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:34:27,112 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-21 08:34:27,112 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-21 08:34:27,113 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 3232c7c8-9929-4cdc-938e-b55620af7dad, 会话ID: None
2025-08-21 08:34:27,113 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 3232c7c8-9929-4cdc-938e-b55620af7dad, 会话ID: None
2025-08-21 08:34:30,062 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:34:30,062 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:34:32,011 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-21 08:34:32,011 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-21 08:34:40,046 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:34:40,046 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:34:50,041 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:34:50,041 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:35:00,055 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:35:00,055 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:35:10,074 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:35:10,074 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:35:20,046 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:35:20,046 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:35:30,048 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:35:30,048 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:35:40,053 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:35:40,053 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:35:50,056 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:35:50,056 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:36:00,051 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:36:00,051 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:36:10,049 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:36:10,049 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:36:20,047 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:36:20,047 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:36:30,063 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:36:30,063 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:36:40,057 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:36:40,057 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:36:49,501 - app.services.test_plan_generator - INFO - 流式生成完成，总长度: 5674
2025-08-21 08:36:49,501 - app.services.test_plan_generator - INFO - 流式生成完成，总长度: 5674
2025-08-21 08:36:49,509 - app.api.test_plan - INFO - 流式测试计划记录已保存 - 记录ID: 7bc2521f-c583-490f-8767-e1168714f2bd
2025-08-21 08:36:49,509 - app.api.test_plan - INFO - 流式测试计划记录已保存 - 记录ID: 7bc2521f-c583-490f-8767-e1168714f2bd
2025-08-21 08:36:49,966 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:36:49,966 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:37:00,808 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:37:00,808 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:37:11,695 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:37:11,695 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:37:20,746 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:37:20,746 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:37:30,722 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:37:30,722 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:37:41,706 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:37:41,706 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:37:50,703 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:37:50,703 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:38:00,742 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:38:00,742 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:38:11,743 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:38:11,743 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:38:20,805 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:38:20,805 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:38:30,721 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:38:30,721 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:38:41,738 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:38:41,738 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:38:50,677 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:38:50,677 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:39:00,710 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:39:00,710 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:39:10,729 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:39:10,729 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:39:20,747 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:39:20,747 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:39:30,670 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:39:30,670 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:39:40,784 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:39:40,784 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:39:50,662 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:39:50,662 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:40:00,678 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:40:00,678 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:40:10,748 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:40:10,748 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:40:20,723 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:40:20,723 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:40:30,701 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:40:30,701 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:40:41,869 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:40:41,869 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:40:50,706 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:40:50,706 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:00,793 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:00,793 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:10,740 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:10,740 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:20,678 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:20,678 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:30,052 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:30,052 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:30,927 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:30,927 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:41,918 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:41,918 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:42,522 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 07d40ccb-a3f4-4b8e-b9cb-f39d9f3e9104
2025-08-21 08:41:42,522 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: 07d40ccb-a3f4-4b8e-b9cb-f39d9f3e9104
2025-08-21 08:41:42,523 - app.services.test_plan_generator - INFO - 开始生成测试计划: 在淘宝首页搜索iPhone15，查看第一个商品详情，截图保存后返回首页...
2025-08-21 08:41:42,523 - app.services.test_plan_generator - INFO - 开始生成测试计划: 在淘宝首页搜索iPhone15，查看第一个商品详情，截图保存后返回首页...
2025-08-21 08:41:42,524 - app.services.test_plan_generator - INFO - 从文本中检测到平台: IOS
2025-08-21 08:41:42,524 - app.services.test_plan_generator - INFO - 从文本中检测到平台: IOS
2025-08-21 08:41:42,540 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:42,540 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:42,542 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-21 08:41:42,542 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-21 08:41:42,543 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 7d77ba7c-842c-49d1-b11f-ee092a6d6f54, 会话ID: None
2025-08-21 08:41:42,543 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: 7d77ba7c-842c-49d1-b11f-ee092a6d6f54, 会话ID: None
2025-08-21 08:41:44,021 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-21 08:41:44,021 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-21 08:41:51,025 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:41:51,025 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:42:01,033 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:42:01,033 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:42:11,938 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:42:11,938 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:42:21,025 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:42:21,025 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:42:31,033 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:42:31,033 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:42:41,975 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:42:41,975 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:42:51,029 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:42:51,029 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:43:01,061 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:43:01,061 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:43:12,003 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:43:12,003 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:43:21,028 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:43:21,028 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:43:31,034 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:43:31,034 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:43:42,021 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:43:42,021 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:43:51,033 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:43:51,033 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:44:01,032 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:44:01,032 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:44:12,043 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:44:12,043 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:44:21,096 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:44:21,096 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:44:31,050 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:44:31,050 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:44:41,043 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:44:41,043 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:44:51,029 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:44:51,029 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:45:01,033 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:45:01,033 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:45:11,062 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:45:11,062 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:45:16,019 - app.services.test_plan_generator - INFO - 流式生成完成，总长度: 8215
2025-08-21 08:45:16,019 - app.services.test_plan_generator - INFO - 流式生成完成，总长度: 8215
2025-08-21 08:45:16,030 - app.api.test_plan - INFO - 流式测试计划记录已保存 - 记录ID: 07d40ccb-a3f4-4b8e-b9cb-f39d9f3e9104
2025-08-21 08:45:16,030 - app.api.test_plan - INFO - 流式测试计划记录已保存 - 记录ID: 07d40ccb-a3f4-4b8e-b9cb-f39d9f3e9104
2025-08-21 08:45:21,763 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:45:21,763 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:45:31,742 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:45:31,742 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:45:42,133 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:45:42,133 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:45:51,680 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:45:51,680 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:46:01,738 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:46:01,738 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:46:12,146 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:46:12,146 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:46:21,710 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:46:21,710 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:46:31,747 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:46:31,747 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:46:42,184 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:46:42,184 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:46:51,711 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:46:51,711 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:47:01,706 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:47:01,706 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:47:12,202 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:47:12,202 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:47:21,716 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:47:21,716 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:47:31,696 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:47:31,696 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:47:42,226 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:47:42,226 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:47:51,726 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:47:51,726 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:48:01,796 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:48:01,796 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:48:12,249 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:48:12,249 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:48:21,716 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:48:21,716 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:48:31,703 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:48:31,703 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:48:42,286 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:48:42,286 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:48:51,673 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:48:51,673 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:49:02,016 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:49:02,016 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:49:12,301 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:49:12,301 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:49:21,751 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:49:21,751 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:49:31,713 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:49:31,713 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:49:42,318 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:49:42,318 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:49:51,710 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:49:51,710 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:50:01,765 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:50:01,765 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:50:12,344 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:50:12,344 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:50:21,755 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:50:21,755 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:50:31,710 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:50:31,710 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:50:42,378 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:50:42,378 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:50:51,688 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:50:51,688 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:51:01,706 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:51:01,706 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:51:12,395 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:51:12,395 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:51:21,768 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:51:21,768 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:51:31,713 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:51:31,713 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:51:42,422 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:51:42,422 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:51:51,698 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:51:51,698 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:52:02,217 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:52:02,217 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:52:12,450 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:52:12,450 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:52:21,765 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:52:21,765 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:52:31,676 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:52:31,676 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:52:42,480 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:52:42,480 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:52:51,704 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:52:51,704 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:53:01,707 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:53:01,707 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:53:12,505 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 08:53:12,505 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:17:11,866 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:17:11,866 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:17:21,876 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:17:21,876 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:17:32,579 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:17:32,579 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:17:42,524 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:17:42,524 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:17:52,530 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:17:52,530 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:18:02,598 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:18:02,598 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:18:10,975 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: efd4d2f8-a722-4d82-b01a-f08492de7767
2025-08-21 09:18:10,975 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: efd4d2f8-a722-4d82-b01a-f08492de7767
2025-08-21 09:18:10,976 - app.services.test_plan_generator - INFO - 开始生成测试计划: 打开美团外卖APP，搜索火锅，选择第一家店，加入购物车后下单...
2025-08-21 09:18:10,976 - app.services.test_plan_generator - INFO - 开始生成测试计划: 打开美团外卖APP，搜索火锅，选择第一家店，加入购物车后下单...
2025-08-21 09:18:10,977 - app.services.test_plan_generator - INFO - 轮询选择平台: ANDROID
2025-08-21 09:18:10,977 - app.services.test_plan_generator - INFO - 轮询选择平台: ANDROID
2025-08-21 09:18:10,987 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:18:10,987 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:18:10,988 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-21 09:18:10,988 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-21 09:18:10,988 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: f1c43df3-3a36-419b-b29c-f471c31f2831, 会话ID: None
2025-08-21 09:18:10,988 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: f1c43df3-3a36-419b-b29c-f471c31f2831, 会话ID: None
2025-08-21 09:18:11,859 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:18:11,859 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:18:12,253 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-21 09:18:12,253 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-21 09:18:21,864 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:18:21,864 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:18:32,611 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:18:32,611 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:18:41,862 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:18:41,862 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:18:51,864 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:18:51,864 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:19:02,643 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:19:02,643 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:19:12,013 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:19:12,013 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:19:22,525 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:19:22,525 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:19:32,668 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:19:32,668 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:19:42,525 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:19:42,525 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:19:51,865 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:19:51,865 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:20:02,678 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:20:02,678 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:20:11,868 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:20:11,868 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:20:22,527 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:20:22,527 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:20:32,709 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:20:32,709 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:20:41,866 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:20:41,866 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:20:51,867 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:20:51,867 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:21:02,727 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:21:02,727 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:21:11,863 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:21:11,863 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:21:21,889 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:21:21,889 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:21:32,772 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:21:32,772 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:21:41,868 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:21:41,868 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:21:51,865 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:21:51,865 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:22:02,782 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:22:02,782 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:22:11,862 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:22:11,862 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:22:21,869 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:22:21,869 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:22:32,794 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:22:32,794 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:22:41,866 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:22:41,866 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:22:51,867 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:22:51,867 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:23:02,825 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:23:02,825 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:23:11,863 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:23:11,863 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:23:21,866 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:23:21,866 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:23:32,850 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:23:32,850 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:23:37,688 - app.services.test_plan_generator - INFO - 流式生成完成，总长度: 12651
2025-08-21 09:23:37,688 - app.services.test_plan_generator - INFO - 流式生成完成，总长度: 12651
2025-08-21 09:23:37,696 - app.api.test_plan - INFO - 流式测试计划记录已保存 - 记录ID: efd4d2f8-a722-4d82-b01a-f08492de7767
2025-08-21 09:23:37,696 - app.api.test_plan - INFO - 流式测试计划记录已保存 - 记录ID: efd4d2f8-a722-4d82-b01a-f08492de7767
2025-08-21 09:23:41,863 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:23:41,863 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:23:51,875 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:23:51,875 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:24:02,878 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:24:02,878 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:24:12,531 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:24:12,531 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:24:22,524 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:24:22,524 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:24:32,905 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:24:32,905 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:24:42,527 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:24:42,527 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:24:52,538 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:24:52,538 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:25:02,933 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:25:02,933 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:25:12,533 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:25:12,533 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:25:22,566 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:25:22,566 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:25:35,565 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:25:35,565 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:26:22,241 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:26:22,241 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:26:32,985 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:26:32,985 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:26:42,539 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:26:42,539 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:26:52,546 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:26:52,546 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:27:03,012 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:27:03,012 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:27:12,532 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:27:12,532 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:27:22,530 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:27:22,530 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:27:35,569 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:27:35,569 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:28:42,004 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-21 09:28:42,004 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-21 09:28:42,004 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-21 09:28:42,004 - __main__ - INFO - 内存阈值: 90.0%
2025-08-21 09:28:42,470 - app.services.test_plan_generator - INFO - 尝试加载prompt模板: /Users/<USER>/Desktop/before_work/local_overall_api/app/data/prompts/test_plan_prompts.json
2025-08-21 09:28:42,471 - app.services.test_plan_generator - INFO - prompt模板加载成功
2025-08-21 09:28:42,471 - app.services.test_plan_generator - INFO - 测试计划生成器初始化成功，默认模型: deepseek-r1:32b
2025-08-21 09:28:42,509 - app - INFO - 正在启动 Overall API Server...
2025-08-21 09:28:42,509 - app - INFO - 正在启动 Overall API Server...
2025-08-21 09:28:42,520 - app.models.database - INFO - 数据库初始化成功
2025-08-21 09:28:42,520 - app.models.database - INFO - 数据库初始化成功
2025-08-21 09:28:42,520 - app - INFO - 数据库初始化完成
2025-08-21 09:28:42,520 - app - INFO - 数据库初始化完成
2025-08-21 09:28:42,535 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:28:42,535 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:28:42,535 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-21 09:28:42,535 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-21 09:28:42,535 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-21 09:28:42,535 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-21 09:28:42,535 - app - INFO - 内存监控服务已启动
2025-08-21 09:28:42,535 - app - INFO - 内存监控服务已启动
2025-08-21 09:28:42,535 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-21 09:28:42,535 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-21 09:28:42,535 - app - INFO - 任务队列服务已启动
2025-08-21 09:28:42,535 - app - INFO - 任务队列服务已启动
2025-08-21 09:28:42,535 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-21 09:28:42,535 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-21 09:28:42,536 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-21 09:28:42,536 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-21 09:28:42,536 - app - INFO - 会话管理器已启动
2025-08-21 09:28:42,536 - app - INFO - 会话管理器已启动
2025-08-21 09:28:42,536 - app - INFO - Overall API Server 启动完成!
2025-08-21 09:28:42,536 - app - INFO - Overall API Server 启动完成!
2025-08-21 09:29:35,563 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:29:35,563 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:30:35,572 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:30:35,572 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:31:35,566 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:31:35,566 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:32:02,114 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-21 09:32:02,114 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-21 09:32:02,114 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-21 09:32:02,114 - __main__ - INFO - 内存阈值: 90.0%
2025-08-21 09:32:02,526 - app.services.test_plan_generator - INFO - 尝试加载prompt模板: /Users/<USER>/Desktop/before_work/local_overall_api/app/data/prompts/test_plan_prompts.json
2025-08-21 09:32:02,526 - app.services.test_plan_generator - INFO - prompt模板加载成功
2025-08-21 09:32:02,526 - app.services.test_plan_generator - INFO - 测试计划生成器初始化成功，默认模型: deepseek-r1:32b
2025-08-21 09:32:02,561 - app - INFO - 正在启动 Overall API Server...
2025-08-21 09:32:02,561 - app - INFO - 正在启动 Overall API Server...
2025-08-21 09:32:02,571 - app.models.database - INFO - 数据库初始化成功
2025-08-21 09:32:02,571 - app.models.database - INFO - 数据库初始化成功
2025-08-21 09:32:02,571 - app - INFO - 数据库初始化完成
2025-08-21 09:32:02,571 - app - INFO - 数据库初始化完成
2025-08-21 09:32:02,586 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:32:02,586 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:32:02,586 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-21 09:32:02,586 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-21 09:32:02,586 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-21 09:32:02,586 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-21 09:32:02,586 - app - INFO - 内存监控服务已启动
2025-08-21 09:32:02,586 - app - INFO - 内存监控服务已启动
2025-08-21 09:32:02,586 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-21 09:32:02,586 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-21 09:32:02,586 - app - INFO - 任务队列服务已启动
2025-08-21 09:32:02,586 - app - INFO - 任务队列服务已启动
2025-08-21 09:32:02,586 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-21 09:32:02,586 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-21 09:32:02,586 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-21 09:32:02,586 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-21 09:32:02,586 - app - INFO - 会话管理器已启动
2025-08-21 09:32:02,586 - app - INFO - 会话管理器已启动
2025-08-21 09:32:02,586 - app - INFO - Overall API Server 启动完成!
2025-08-21 09:32:02,586 - app - INFO - Overall API Server 启动完成!
2025-08-21 09:32:11,498 - app.api.test_plan - ERROR - 获取模型信息失败: Object of type coroutine is not JSON serializable
2025-08-21 09:32:11,498 - app.api.test_plan - ERROR - 获取模型信息失败: Object of type coroutine is not JSON serializable
2025-08-21 09:32:35,578 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:32:35,578 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:32:44,543 - app.api.test_plan - ERROR - 获取模型信息失败: Object of type coroutine is not JSON serializable
2025-08-21 09:32:44,543 - app.api.test_plan - ERROR - 获取模型信息失败: Object of type coroutine is not JSON serializable
2025-08-21 09:33:01,601 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-21 09:33:01,601 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-21 09:33:01,601 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-21 09:33:01,601 - __main__ - INFO - 内存阈值: 90.0%
2025-08-21 09:33:02,022 - app.services.test_plan_generator - INFO - 尝试加载prompt模板: /Users/<USER>/Desktop/before_work/local_overall_api/app/data/prompts/test_plan_prompts.json
2025-08-21 09:33:02,022 - app.services.test_plan_generator - INFO - prompt模板加载成功
2025-08-21 09:33:02,022 - app.services.test_plan_generator - INFO - 测试计划生成器初始化成功，默认模型: deepseek-r1:32b
2025-08-21 09:33:02,056 - app - INFO - 正在启动 Overall API Server...
2025-08-21 09:33:02,056 - app - INFO - 正在启动 Overall API Server...
2025-08-21 09:33:02,066 - app.models.database - INFO - 数据库初始化成功
2025-08-21 09:33:02,066 - app.models.database - INFO - 数据库初始化成功
2025-08-21 09:33:02,066 - app - INFO - 数据库初始化完成
2025-08-21 09:33:02,066 - app - INFO - 数据库初始化完成
2025-08-21 09:33:02,082 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:33:02,082 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:33:02,082 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-21 09:33:02,082 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-21 09:33:02,082 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-21 09:33:02,082 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-21 09:33:02,083 - app - INFO - 内存监控服务已启动
2025-08-21 09:33:02,083 - app - INFO - 内存监控服务已启动
2025-08-21 09:33:02,083 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-21 09:33:02,083 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-21 09:33:02,083 - app - INFO - 任务队列服务已启动
2025-08-21 09:33:02,083 - app - INFO - 任务队列服务已启动
2025-08-21 09:33:02,083 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-21 09:33:02,083 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-21 09:33:02,083 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-21 09:33:02,083 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-21 09:33:02,083 - app - INFO - 会话管理器已启动
2025-08-21 09:33:02,083 - app - INFO - 会话管理器已启动
2025-08-21 09:33:02,083 - app - INFO - Overall API Server 启动完成!
2025-08-21 09:33:02,083 - app - INFO - Overall API Server 启动完成!
2025-08-21 09:33:11,028 - app.services.test_plan_generator - ERROR - 获取模型信息失败: 'OllamaProxyService' object has no attribute 'list_models'
2025-08-21 09:33:11,028 - app.services.test_plan_generator - ERROR - 获取模型信息失败: 'OllamaProxyService' object has no attribute 'list_models'
2025-08-21 09:33:35,563 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:33:35,563 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:33:37,933 - app.services.test_plan_generator - ERROR - 获取模型信息失败: 'OllamaProxyService' object has no attribute 'list_models'
2025-08-21 09:33:37,933 - app.services.test_plan_generator - ERROR - 获取模型信息失败: 'OllamaProxyService' object has no attribute 'list_models'
2025-08-21 09:33:52,026 - __main__ - INFO - 正在启动 Overall API Server...
2025-08-21 09:33:52,026 - __main__ - INFO - 服务器地址: 0.0.0.0:5631
2025-08-21 09:33:52,026 - __main__ - INFO - Ollama后端: http://localhost:11434
2025-08-21 09:33:52,026 - __main__ - INFO - 内存阈值: 90.0%
2025-08-21 09:33:52,443 - app.services.test_plan_generator - INFO - 尝试加载prompt模板: /Users/<USER>/Desktop/before_work/local_overall_api/app/data/prompts/test_plan_prompts.json
2025-08-21 09:33:52,443 - app.services.test_plan_generator - INFO - prompt模板加载成功
2025-08-21 09:33:52,443 - app.services.test_plan_generator - INFO - 测试计划生成器初始化成功，默认模型: deepseek-r1:32b
2025-08-21 09:33:52,479 - app - INFO - 正在启动 Overall API Server...
2025-08-21 09:33:52,479 - app - INFO - 正在启动 Overall API Server...
2025-08-21 09:33:52,488 - app.models.database - INFO - 数据库初始化成功
2025-08-21 09:33:52,488 - app.models.database - INFO - 数据库初始化成功
2025-08-21 09:33:52,488 - app - INFO - 数据库初始化完成
2025-08-21 09:33:52,488 - app - INFO - 数据库初始化完成
2025-08-21 09:33:52,499 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:33:52,499 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:33:52,499 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-21 09:33:52,499 - app - INFO - Ollama服务连接成功: http://localhost:11434
2025-08-21 09:33:52,499 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-21 09:33:52,499 - app.services.memory_monitor - INFO - 内存监控服务已启动，阈值: 90.0%，检查间隔: 5s
2025-08-21 09:33:52,499 - app - INFO - 内存监控服务已启动
2025-08-21 09:33:52,499 - app - INFO - 内存监控服务已启动
2025-08-21 09:33:52,499 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-21 09:33:52,499 - app.services.task_queue - INFO - 任务队列处理器已启动
2025-08-21 09:33:52,499 - app - INFO - 任务队列服务已启动
2025-08-21 09:33:52,499 - app - INFO - 任务队列服务已启动
2025-08-21 09:33:52,499 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-21 09:33:52,499 - app.services.session_manager - ERROR - 从数据库恢复会话失败: 'NoneType' object is not callable
2025-08-21 09:33:52,499 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-21 09:33:52,499 - app.services.session_manager - INFO - 会话管理器已启动，最大会话数: 8，空闲超时: 1800s
2025-08-21 09:33:52,499 - app - INFO - 会话管理器已启动
2025-08-21 09:33:52,499 - app - INFO - 会话管理器已启动
2025-08-21 09:33:52,499 - app - INFO - Overall API Server 启动完成!
2025-08-21 09:33:52,499 - app - INFO - Overall API Server 启动完成!
2025-08-21 09:34:01,341 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:34:01,341 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:34:35,566 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:34:35,566 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:34:44,675 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:34:44,675 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:34:46,451 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:34:46,451 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:34:46,465 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:34:46,465 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:34:56,459 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:34:56,459 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:35:06,456 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:35:06,456 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:35:16,549 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:35:16,549 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:35:26,542 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:35:26,542 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:35:36,449 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:35:36,449 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:35:46,534 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:35:46,534 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:35:56,539 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:35:56,539 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:36:06,535 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:36:06,535 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:36:16,544 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:36:16,544 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:36:26,538 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:36:26,538 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:36:36,533 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:36:36,533 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:37:35,698 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:37:35,698 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:01,400 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:01,400 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:06,456 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:06,456 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:07,779 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:07,779 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:07,780 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:07,780 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:17,784 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:17,784 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:20,702 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: b292079b-0aef-4b6d-bf96-ed8de51ff91b
2025-08-21 09:38:20,702 - app.api.test_plan - INFO - 开始生成测试计划 - 记录ID: b292079b-0aef-4b6d-bf96-ed8de51ff91b
2025-08-21 09:38:20,702 - app.services.test_plan_generator - INFO - 开始生成测试计划: 在美团首页搜索iPhone15，查看第一个商品详情，截图保存后返回首页...
2025-08-21 09:38:20,702 - app.services.test_plan_generator - INFO - 开始生成测试计划: 在美团首页搜索iPhone15，查看第一个商品详情，截图保存后返回首页...
2025-08-21 09:38:20,702 - app.services.test_plan_generator - INFO - 从文本中检测到平台: IOS
2025-08-21 09:38:20,702 - app.services.test_plan_generator - INFO - 从文本中检测到平台: IOS
2025-08-21 09:38:20,709 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:20,709 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:20,710 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-21 09:38:20,710 - app.services.test_plan_generator - INFO - 调用模型 deepseek-r1:32b 进行计划生成
2025-08-21 09:38:20,711 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: f617c3d7-1c9b-4343-bdc6-b81a1ef620aa, 会话ID: None
2025-08-21 09:38:20,711 - app.services.ollama_proxy - INFO - 开始流式generate请求 - 模型: deepseek-r1:32b, 任务ID: f617c3d7-1c9b-4343-bdc6-b81a1ef620aa, 会话ID: None
2025-08-21 09:38:22,268 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-21 09:38:22,268 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-21 09:38:27,790 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:27,790 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:37,780 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:37,780 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:48,563 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:48,563 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:58,531 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:38:58,531 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:39:07,808 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:39:07,808 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:39:18,536 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:39:18,536 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:39:28,534 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:39:28,534 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:39:38,529 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:39:38,529 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:39:48,537 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:39:48,537 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:39:58,533 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:39:58,533 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:40:08,542 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:40:08,542 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:40:35,838 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:40:35,838 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:41:26,635 - app.services.test_plan_generator - INFO - 流式生成完成，总长度: 6224
2025-08-21 09:41:26,635 - app.services.test_plan_generator - INFO - 流式生成完成，总长度: 6224
2025-08-21 09:41:26,648 - app.api.test_plan - INFO - 流式测试计划记录已保存 - 记录ID: b292079b-0aef-4b6d-bf96-ed8de51ff91b
2025-08-21 09:41:26,648 - app.api.test_plan - INFO - 流式测试计划记录已保存 - 记录ID: b292079b-0aef-4b6d-bf96-ed8de51ff91b
2025-08-21 09:41:33,253 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:41:33,253 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:41:33,254 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:41:33,254 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:41:35,867 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:41:35,867 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:41:40,115 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:41:40,115 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:41:40,115 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:41:40,115 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:41:44,462 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:41:44,462 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:41:50,113 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:41:50,113 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:42:00,118 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:42:00,118 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:42:10,130 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:42:10,130 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:42:20,113 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:42:20,113 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:42:30,123 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:42:30,123 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:42:40,125 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:42:40,125 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:42:50,537 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:42:50,537 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:43:00,537 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:43:00,537 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:43:10,532 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
2025-08-21 09:43:10,532 - httpx - INFO - HTTP Request: GET http://localhost:11434/api/tags "HTTP/1.1 200 OK"
