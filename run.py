#!/usr/bin/env python3
"""
Overall API Server 启动脚本
"""

import asyncio
import uvicorn
import logging
from pathlib import Path

# 确保在导入应用之前设置好环境
import sys
sys.path.insert(0, str(Path(__file__).parent))

from app.config import config
from app.utils.logging_config import setup_logging

def main():
    """主函数"""
    try:
        # 确保必要目录存在
        config.ensure_directories()
        
        # 设置日志
        setup_logging()
        logger = logging.getLogger(__name__)
        
        logger.info("正在启动 Overall API Server...")
        logger.info(f"服务器地址: {config.server_host}:{config.server_port}")
        logger.info(f"Ollama后端: {config.ollama_base_url}")
        logger.info(f"内存阈值: {config.memory_threshold * 100}%")
        
        # 启动服务器
        uvicorn.run(
            "app.main:app",
            host=config.server_host,
            port=config.server_port,
            workers=config.get('server.workers', 1),
            log_level=config.get('server.log_level', 'info'),
            access_log=True,
            reload=False  # 生产环境关闭自动重载
        )
        
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()